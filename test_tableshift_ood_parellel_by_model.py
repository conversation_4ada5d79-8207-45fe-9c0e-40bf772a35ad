import torch
from tableshift import get_dataset
from sklearn.model_selection import train_test_split
from model_registry import get_registered_models, get_model_class, ClassifierBase
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
import tabular_metrics
import numpy as np
import pandas as pd
import os
import datetime
from tqdm import tqdm
import time
import traceback
import gc
from concurrent.futures import ProcessPoolExecutor, as_completed, ThreadPoolExecutor
import json
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import MinMaxScaler
import shutil
import random

def init_seed(seed, reproducibility):
    r""" init random seed for random functions in numpy, torch, cuda and cudnn

    Args:
        seed (int): random seed
        reproducibility (bool): Whether to require reproducibility
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    if reproducibility:
        torch.backends.cudnn.benchmark = False
        torch.backends.cudnn.deterministic = True
    else:
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False

init_seed(3061,True)

# Load data

time_now = datetime.datetime.now()
save_root  = 'ood_test/' + time_now.strftime("%Y%m%d_%H-%M-%S") # 输出路径
os.makedirs(save_root, exist_ok=True)
n_repeat_times = 5

gpu_ids = [0,1,2,3,4,5,6,7]

def remove_pycache(root='.'):
    for dirpath, dirnames, filenames in os.walk(root):
        for dirname in dirnames:
            if dirname == "__pycache__":
                full_path = os.path.join(dirpath, dirname)
                shutil.rmtree(full_path)
                print(f"Removed {full_path}")

def chunk_prediction(model, test_x, test_y, chunk_size):
    roc_values = []
    ce_values = []
    acc_values = []
    balanced_acc_values = []

    test_x_df_chunks = np.array_split(test_x, int(np.ceil(len(test_x) / chunk_size)))
    test_y_chunks = np.array_split(test_y, int(np.ceil(len(test_y) / chunk_size)))
    
    for test_x_df_chunk, test_y_chunk in zip(test_x_df_chunks, test_y_chunks):
        (roc_chunk, ce_chunk, acc_chunk, balanced_acc_chunk) = model.predict(test_x_df_chunk, test_y_chunk)
        roc_values.append(roc_chunk)
        ce_values.append(ce_chunk)
        acc_values.append(acc_chunk)
        balanced_acc_values.append(balanced_acc_chunk)

    roc = float(np.mean(roc_values))
    ce = float(np.mean(ce_values))
    acc = float(np.mean(acc_values))
    balanced_acc = float(np.mean(balanced_acc_values))

    return roc, ce, acc, balanced_acc

def chunk_prediction_ldm(model, train_x, train_y, test_x, test_y, chunk_size):
    roc_values = []
    ce_values = []
    acc_values = []
    balanced_acc_values = []

    test_x_df_chunks = np.array_split(test_x, int(np.ceil(len(test_x) / chunk_size)))
    test_y_chunks = np.array_split(test_y, int(np.ceil(len(test_y) / chunk_size)))
    
    for test_x_df_chunk, test_y_chunk in zip(test_x_df_chunks, test_y_chunks):
        (roc_chunk, ce_chunk, acc_chunk, balanced_acc_chunk) = model.predict(train_x, train_y, test_x_df_chunk, test_y_chunk)
        roc_values.append(roc_chunk)
        ce_values.append(ce_chunk)
        acc_values.append(acc_chunk)
        balanced_acc_values.append(balanced_acc_chunk)

    roc = float(np.mean(roc_values))
    ce = float(np.mean(ce_values))
    acc = float(np.mean(acc_values))
    balanced_acc = float(np.mean(balanced_acc_values))

    return roc, ce, acc, balanced_acc

def run_model_steps(model_name, train_tuple, id_test_tuple, ood_test_tuple, data_info, gpu_id):
    # if gpu_id is not None:
    #     os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    #     torch.cuda.set_device(0) 
    #     print(f"{model_name} Set CUDA_VISIBLE_DEVICES to: {os.environ['CUDA_VISIBLE_DEVICES']}")
    # else:
    #     print(f"{model_name} No GPU specified, using CPU.")
    
    model_class = get_model_class(model_name)
    model_instance:ClassifierBase = model_class(gpu_id=gpu_id)

    model_df_list = ["lgb_classifier",
                     "fttransformer_classifier",
                     "tabr_classifier",
                     "modernnca_classifier"]
    train_x, train_y, train_x_df = train_tuple
    id_test_x, id_test_y, id_test_x_df = id_test_tuple
    ood_test_x, ood_test_y, ood_test_x_df = ood_test_tuple

    
    if 'ldm' not in model_name:
        if model_name in model_df_list:
            print(f'Fit model {model_name}...')
            model_instance.fit(train_x_df, train_y)

            print(f'Test model {model_name} on id test samples...')
            chunk_size = 10000
            if len(id_test_x_df) > chunk_size:
                (id_roc, id_ce, id_acc, id_balanced_acc) = chunk_prediction(model_instance, id_test_x_df, id_test_y, chunk_size)
            else:
                (id_roc, id_ce, id_acc, id_balanced_acc) = model_instance.predict(id_test_x_df, id_test_y)

            print(f'Test model {model_name} on ood test samples...')
            if len(ood_test_x_df) > chunk_size:
                (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = chunk_prediction(model_instance, ood_test_x_df, ood_test_y, chunk_size)
            else:
                (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = model_instance.predict(ood_test_x_df, ood_test_y)

        else:
            model_instance.fit(train_x, train_y)
            print(f'Test model {model_name} on id test samples...')
            chunk_size = 10000
            if len(id_test_x) > chunk_size:
                (id_roc, id_ce, id_acc, id_balanced_acc) = chunk_prediction(model_instance, id_test_x, id_test_y, chunk_size)
            else:
                (id_roc, id_ce, id_acc, id_balanced_acc) = model_instance.predict(id_test_x, id_test_y)
            
            print(f'Test model {model_name} on ood test samples...')
            if len(ood_test_x) > chunk_size:
                (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = chunk_prediction(model_instance, ood_test_x, ood_test_y, chunk_size)
            else:
                (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = model_instance.predict(ood_test_x, ood_test_y)
    else:
        print(f'Test model {model_name} on id test samples...')
        dataset_name = data_info['dataset name']
        if 'anes' in dataset_name:
            chunk_size = 100
        else:
            chunk_size = 5000
        if len(id_test_x) > chunk_size:
            (id_roc, id_ce, id_acc, id_balanced_acc) = chunk_prediction_ldm(model_instance, train_x, train_y, id_test_x, id_test_y, chunk_size)
        else:
            (id_roc, id_ce, id_acc, id_balanced_acc) = model_instance.predict(train_x, train_y, id_test_x, id_test_y)

        print(f'Test model {model_name} on ood test samples...')
        if len(ood_test_x) > chunk_size:
            (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = chunk_prediction_ldm(model_instance, train_x, train_y, ood_test_x, ood_test_y, chunk_size)
        else:
            (ood_roc, ood_ce, ood_acc, ood_balanced_acc) = model_instance.predict(train_x, train_y, ood_test_x, ood_test_y)

        gc.collect()  # 强制进行垃圾回收
        torch.cuda.empty_cache()
    
    rst_dict = data_info
    rst_dict["model_name"] = model_name
    rst_dict["id_auc"+model_name] = id_roc
    rst_dict["id_acc"+model_name] = id_acc
    rst_dict["id_ce"+model_name] = id_ce
    rst_dict["id_balanced_acc"+model_name] = id_balanced_acc
    rst_dict["ood_auc"+model_name] = ood_roc
    rst_dict["ood_acc"+model_name] = ood_acc
    rst_dict["ood_ce"+model_name] = ood_ce
    rst_dict["ood_balanced_acc"+model_name] = ood_balanced_acc

    print(f"Finished {model_name}.") 
    return rst_dict

def load_data(dataset_name):
    if dataset_name == 'physionet':
        cache_dir = '/mnt/public/lxy/data/tableshift/physionet'
    elif dataset_name == 'mimic_extract_los_3' or dataset_name == 'mimic_extract_mort_hosp':
        cache_dir = '/mnt/public/lxy/data/tableshift/MIMIC-III'
    else:
        cache_dir = '/mnt/public/lxy/data/tableshift'

    dset = get_dataset(dataset_name, cache_dir)
    X_train, y_train, tr_groups, train_domains = dset.get_pandas("train")
    X_id_test, y_id_test, id_test_groups, id_test_domains = dset.get_pandas("id_test")
    X_ood_test, y_ood_test, ood_test_groups, ood_test_domains = dset.get_pandas("ood_test")

    # Drop unique value columns
    low_variance_cols = X_train.columns[X_train.nunique() <= 1]
    print("Dropping low variance columns:", low_variance_cols.tolist())
    X_train = X_train.drop(columns=low_variance_cols)
    X_id_test = X_id_test.drop(columns=low_variance_cols)
    X_ood_test = X_ood_test.drop(columns=low_variance_cols)

    # 对每一列进行检查
    for col in X_train.columns:
        if X_train[col].dtype == 'object':  # 检查是否是字符串列
            try:
                le = LabelEncoder()
                X_train[col] = le.fit_transform(X_train[col])
                X_id_test[col] = le.transform(X_id_test[col])  # 确保测试集使用相同的编码
                X_ood_test[col] = le.transform(X_ood_test[col])  # 确保测试集使用相同的编码
            except Exception as e:
                X_train = X_train.drop(columns=[col])
                X_id_test = X_id_test.drop(columns=[col])
                X_ood_test = X_ood_test.drop(columns=[col])
    

    scaler = MinMaxScaler()
    X_train_scale = scaler.fit_transform(X_train)
    X_id_test_scale = scaler.transform(X_id_test)
    X_ood_test_scale = scaler.transform(X_ood_test)

    le_y = LabelEncoder()
    y_train = le_y.fit_transform(y_train)
    y_id_test = le_y.transform(y_id_test) 
    y_ood_test = le_y.transform(y_ood_test) 

    trainX, trainy = X_train_scale, y_train
    id_testX, id_testy = X_id_test_scale, y_id_test
    ood_testX, ood_testy = X_ood_test_scale, y_ood_test
    
    # 新增类型转换代码
    trainX = np.asarray(trainX, dtype=np.float32)
    trainy = np.asarray(trainy, dtype=np.int64)

    id_testX = np.asarray(id_testX, dtype=np.float32)
    id_testy = np.asarray(id_testy, dtype=np.int64)

    ood_testX = np.asarray(ood_testX, dtype=np.float32)
    ood_testy = np.asarray(ood_testy, dtype=np.int64)

    data_info = {'dataset name':f'{dataset_name}',
        'num_feat':len(X_train.columns), 
        'num_class':len(np.unique(y_train))
        }
    
    column_names = list(X_train.columns)

    return (trainX, trainy), (id_testX, id_testy), (ood_testX, ood_testy), (train_domains, id_test_domains, ood_test_domains), data_info, column_names

def sample_data(trainX, trainy, id_testX, id_testy, ood_testX, ood_testy, train_domains, id_test_domains, ood_test_domains, data_info, column_names, max_train_size=10000, max_test_size=10000):
    # Data subsampling (make train, id_test, ood_test size <= 10000) (stratified by domains)
    subsample_train_ratio = max_train_size / len(trainX)
    subsample_id_test_ratio = max_test_size / len(id_testX)
    subsample_ood_test_ratio = max_test_size / len(ood_testX)

    if len(trainX) > max_train_size:
        stratify_arg_train = train_domains if train_domains.value_counts().min() > 1 else None
        trainX_sub_sampled, _, trainy_sub, _ = train_test_split(trainX, trainy, test_size=1-subsample_train_ratio, stratify=stratify_arg_train)
        mask = [len(np.unique(trainX_sub_sampled[:, i])) > 1 for i in range(trainX_sub_sampled.shape[1])]
        trainX_sub = trainX_sub_sampled[:, mask]
    else:
        trainX_sub = trainX
        trainy_sub = trainy
    if len(id_testX) > max_test_size:
        stratify_arg_id_test = id_test_domains if id_test_domains.value_counts().min() > 1 else None
        id_testX_sub_sampled, _, id_testy_sub, _ = train_test_split(id_testX, id_testy, test_size=1-subsample_id_test_ratio, stratify=stratify_arg_id_test)
        id_testX_sub = id_testX_sub_sampled[:, mask]
    else:
        id_testX_sub = id_testX[:, mask]
        id_testy_sub = id_testy
    if len(ood_testX) > max_test_size:
        stratify_arg_ood_test = ood_test_domains if ood_test_domains.value_counts().min() > 1 else None
        ood_testX_sub_sampled, _, ood_testy_sub, _ = train_test_split(ood_testX, ood_testy, test_size=1-subsample_ood_test_ratio, stratify=stratify_arg_ood_test)
        ood_testX_sub = ood_testX_sub_sampled[:, mask]
    else:
        ood_testX_sub = ood_testX[:, mask]
        ood_testy_sub = ood_testy
    print(f"original sizes: train {len(trainX)}, id test {len(id_testX)}, ood test {len(ood_testX)}")
    print(f"subsampled train sizes: train {len(trainX_sub)}, id test {len(id_testX_sub)}, ood test {len(ood_testX_sub)}",)
    
    columns = [col for col, m in zip(column_names, mask) if m]
    trainX_sub_df = pd.DataFrame(trainX_sub, columns=columns)
    id_testX_sub_df = pd.DataFrame(id_testX_sub, columns=columns)
    ood_testX_sub_df = pd.DataFrame(ood_testX_sub, columns=columns)

    data_info['num_data_train'] = len(trainX_sub)
    data_info['num_data_id_test'] = len(id_testX_sub)
    data_info['num_data_ood_test'] = len(ood_testX_sub)

    return (trainX_sub, trainy_sub, trainX_sub_df), (id_testX_sub, id_testy_sub, id_testX_sub_df), (ood_testX_sub, ood_testy_sub, ood_testX_sub_df), data_info

def run_experiment(model_names: list, data_list: list, n_repeat_times, gpu_ids):
    rsts_all_dataset = []

    for i in tqdm(range(len(data_list))):
        dataset_name = data_list[i]
        print(f'Evaluation on dataset {dataset_name}')

        (trainX, trainy), (id_testX, id_testy), (ood_testX, ood_testy), (train_domains, id_test_domains, ood_test_domains), data_info, column_names = load_data(dataset_name)
        for n in range(n_repeat_times):
            train_tuple, id_test_tuple, ood_test_tuple, data_info = sample_data(trainX, trainy, id_testX, id_testy, ood_testX, ood_testy, train_domains, id_test_domains, ood_test_domains, data_info, column_names)
            data_info['dataset name'] = f'{dataset_name}_repeat_{n}'

            # Use pool to parallelize model evaluation
            with ProcessPoolExecutor(max_workers=len(model_names)) as executor:
                futures = [
                    executor.submit(run_model_steps, model_name, train_tuple, id_test_tuple, ood_test_tuple, data_info, gpu_ids[i % len(gpu_ids)]) for i, model_name in enumerate(model_names)
                ]
                rst_models = {}
                for future in as_completed(futures):
                    # 从result中获取模型名称
                    model_name = future.result().get("model_name", None)
                    try:
                        for k, v in future.result().items():
                            if isinstance(v, torch.Tensor):
                                future.result()[k] = float(v.item())
                        rst_models[model_name] = future.result()
                    except Exception as e:
                        print(f"Error occurred while running {model_name}: {e}")
                        print("Traceback:", traceback.format_exc())  # 打印完整堆栈

            print("All models have finished.")
            print("Results dictionary:", rst_models)
            rst = {k: v for d in rst_models.values() for k, v in d.items()}
            rsts_all_dataset.append(rst)
            rst_dict_file_name = f'dataset_{dataset_name}_repeat_{n}_rst.json'
            with open(os.path.join(save_root, rst_dict_file_name), 'w') as f:
                json.dump(rst, f, indent=4)
    rsts_all_dataset = pd.DataFrame(rsts_all_dataset)
    file_name = 'tableshift_all_rsts.csv'
    rsts_all_dataset.to_csv(os.path.join(save_root, file_name), index=False) 

if __name__ == "__main__":
    data_list = [#'anes',
                'assistments',
                'nhanes_lead',
                'college_scorecard',
                'brfss_diabetes',
                'acsfoodstamps',
                'diabetes_readmission',
                'brfss_blood_pressure',
                #'mimic_extract_los_3',
                #'mimic_extract_mort_hosp',
                'acsincome',
                'acspubcov',
                'physionet',
                'acsunemployment'
                ]

    model_list = ["autogluon_classifier",
                #   "logistic_classifier", 
                #   "xgboost_classifier", 
                #   "lgb_classifier", 
                #   "gbm_classifier",
                #   "tabpfn_classifier", 
                #   "ldm_classifier", 
                #   "ldm16m_classifier", 
                #   "tabicl_classifier", 
                #   "tabdpt_classifier", 
                #   "fttransformer_classifier", 
                #   "tabr_classifier", 
                #   "modernnca_classifier",
                #   "mlp_classifier",
                #   "node_classifier",
                #   "resnet_classifier",
                #   # "saint_classifier",
                #   "tangos_classifier"
                  ]
    
    gpu_ids = [0,1,2,3,4,5,6,7]
    start_time = time.time()
    run_experiment(model_list, data_list, n_repeat_times, gpu_ids)
    end_time = time.time()
    print(f"Total experiment time: {end_time - start_time} seconds")