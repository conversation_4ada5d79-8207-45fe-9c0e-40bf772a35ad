#  Copyright (c) Prior Labs GmbH 2025.

from __future__ import annotations

import random
import warnings
from collections.abc import Callable, Generator, Iterable
from contextlib import contextmanager
from functools import partial
from typing import Any, Literal

import einops
import networkx as nx
import numpy as np
import torch
from torch import nn
from torch.utils.checkpoint import checkpoint

from maskmodel.encoders import (
    SequentialEncoder,
    get_encoder,
    get_cls_y_encoder,
    get_reg_y_encoder,
)
from maskmodel.layer import PerFeatureEncoderLayer

DEFAULT_EMSIZE = 128

def approx_unique(x, abs_eps=1e-6, rel_eps=5e-4):
    """
    Args:
        x: Tensor of shape (N, L), where N is number of feature vectors.
    Returns:
        unique_counts: Tensor of shape (N,), the approx unique counts for each row.
    """
    x_sorted, _ = torch.sort(x, dim=-1)  # (N, L)
    
    ref_vals = x_sorted[:, :-1]          # (N, L-1)
    next_vals = x_sorted[:, 1:]          # (N, L-1)
    
    diffs = next_vals - ref_vals         # (N, L-1)
    tolerance = abs_eps + rel_eps * torch.abs(ref_vals)
    
    is_new_unique = diffs > tolerance    # (N, L-1)
    unique_counts = is_new_unique.sum(dim=1) + 1  # +1 for the first value
    
    return unique_counts

def detect_categorical_features(x, max_categories_ratio=0.1):
    # x: (seq_len, batch_size, num_features)
    seq_len, batch_size, num_features = x.shape
    max_categories = seq_len * max_categories_ratio
    x = x.permute(1, 2, 0).reshape(-1, seq_len)  # shape: (batch_size * num_features, seq_len)

    unique_counts = approx_unique(x)  # (B * F,)
    categorical_mask = unique_counts <= max_categories
    return categorical_mask.reshape(batch_size, num_features).to(x.device)

@contextmanager
def isolate_torch_rng(seed: int, device: torch.device) -> Generator[None, None, None]:
    torch_rng_state = torch.get_rng_state()
    if torch.cuda.is_available():
        torch_cuda_rng_state = torch.cuda.get_rng_state(device=device)
    torch.manual_seed(seed)
    try:
        yield
    finally:
        torch.set_rng_state(torch_rng_state)
        if torch.cuda.is_available():
            torch.cuda.set_rng_state(torch_cuda_rng_state, device=device)


class LayerStack(nn.Module):
    """Similar to nn.Sequential, but with support for passing keyword arguments
    to layers and stacks the same layer multiple times.
    """

    def __init__(
        self,
        *,
        layer_creator: Callable[[], nn.Module],
        num_layers: int,
        recompute_each_layer: bool = False,
        min_num_layers_layer_dropout: int | None = None,
    ):
        super().__init__()
        self.layers = nn.ModuleList([layer_creator() for _ in range(num_layers)])
        self.num_layers = num_layers
        self.min_num_layers_layer_dropout = (
            min_num_layers_layer_dropout
            if min_num_layers_layer_dropout is not None
            else num_layers
        )
        self.recompute_each_layer = recompute_each_layer
        self.recompute_each_layer = False
    def forward(
        self,
        x: torch.Tensor,
        *,
        half_layers: bool = False,
        **kwargs: Any,
    ) -> torch.Tensor:
        if half_layers:
            assert (
                self.min_num_layers_layer_dropout == self.num_layers
            ), "half_layers only works without layer dropout"
            n_layers = self.num_layers // 2
        else:
            # 已经随机化过了
            n_layers = self.min_num_layers_layer_dropout
            # n_layers = torch.randint(
            #     low=self.min_num_layers_layer_dropout,
            #     high=self.num_layers + 1,
            #     size=(1,),
            # ).item()
            # print('n_layers',n_layers)
        for layer in self.layers[:n_layers]:
            if self.recompute_each_layer and x.requires_grad:
                x = checkpoint(partial(layer, **kwargs), x, use_reentrant=False)  # type: ignore
            else:
                x = layer(x, **kwargs)

        return x


class PerFeatureTransformer(nn.Module): # type: ignore
    """A Transformer model processes a token per feature and sample.

    This model extends the standard Transformer architecture to operate on a
    per-feature basis.
    It allows for processing each feature separately while still leveraging the
    power of self-attention.

    The model consists of an encoder, decoder, and optional components such
    as a feature positional embedding and a separate decoder for each feature.
    """

    # TODO: Feel like this could be simplified a lot from this part downwards
    def __init__(  # noqa: C901, D417, PLR0913
        self,
        *,
        encoder_config: dict[str, Any],
        y_encoder_config: dict[str, Any],
        ninp: int = DEFAULT_EMSIZE,
        nhead: int = 4,
        nhid: int = DEFAULT_EMSIZE * 4,
        nlayers: int = 10,
        decoder_dict: dict[str, tuple[type[nn.Module] | None, int]] | None = None,
        init_method: str | None = None,
        activation: Literal["gelu", "relu"] = "gelu",
        recompute_layer: bool = False,
        min_num_layers_layer_dropout: int | None = None,
        repeat_same_layer: bool = False,
        dag_pos_enc_dim: int = 0,
        features_per_group: int = 1,
        feature_positional_embedding: (
            Literal[
                "normal_rand_vec",
                "uni_rand_vec",
                "learned",
                "subspace",
                "orthogonal",
                "subortho"
            ]
            | None
        ) = None,
        zero_init: bool = True,
        use_separate_decoder: bool = False,
        nlayers_decoder: int | None = None,
        use_encoder_compression_layer: bool = False,
        precomputed_kv: (
            list[torch.Tensor | tuple[torch.Tensor, torch.Tensor]] | None
        ) = None,
        cache_trainset_representation: bool = False,
        enable_mask_feature_pred: bool = False,
        enable_mask_indicator_pred: bool = False,
        enable_feature_attention_mask: bool = False,
        x_encoder=None,
        x_preprocess=None,
        embedding_type="mask_embedding",
        pre_norm: bool = False,
        seed: int | None = None,
        use_categorical: bool = False,
        classify_reg_mixed: bool = False,
        cls_y_num: int = 10,
        # TODO: List explicitly
        **layer_kwargs: Any, 
    ):
        """Initializes the PerFeatureTransformer module.

        Args:
            encoder:
                Pass a nn.Module that takes in a batch of sequences of inputs and
                returns something of the shape (seq_len, batch_size, ninp)
            ninp: Input dimension, also called the embedding dimension
            nhead: Number of attention heads
            nhid: Hidden dimension in the MLP layers
            nlayers:
                Number of layers, each consisting of a multi-head attention layer and
                an MLP layer
            y_encoder:
                A nn.Module that takes in a batch of sequences of outputs and
                returns something of the shape (seq_len, batch_size, ninp)
            decoder_dict: Document this (TODO)
            activation: An activation function, "gelu" or "relu"
            recompute_layer:
                If True, the transformer layers will be recomputed on each
                forward pass in training. This is useful to save memory.
            min_num_layers_layer_dropout:
                If this is set, it enables to drop the last
                layers randomly during training up to this number.
            repeat_same_layer:
                If True, the same layer will be used for all layers.
                This is useful to save memory on weights.
            features_per_group:
                If > 1, the features will be grouped into groups of this
                size and the attention is across groups.
            feature_positional_embedding:
                There is a risk that our models confuse
                features with each other. This positional embedding is added to the
                features to help the model distinguish them.
                We recommend setting this to "subspace".
            zero_init:
                If True, the last sublayer of each attention and MLP layer will
                be initialized with zeros.
                Thus, the layers will start out as identity functions.
            seed: The seed to use for the random number generator.
            use_separate_decoder: If True, the decoder will be separate from the encoder
            nlayers_decoder:
                If use_separate_decoder is True, this is the number of
                layers in the decoder. The default is to use 1/3 of the layers for the
                decoder and 2/3 for the encoder.
            use_encoder_compression_layer: Experimental
            precomputed_kv: Experimental
            layer_kwargs:
                TODO: document.
                for now have a look at layer.py:PerFeatureEncoderLayer.
        """
        if decoder_dict is None:
            decoder_dict = {"standard": (None, 1)}

        super().__init__()

        self.encoder = x_encoder
        self.cls_y_encoder = get_cls_y_encoder(**y_encoder_config)
        self.reg_y_encoder = get_reg_y_encoder(**y_encoder_config)
        self.ninp = ninp
        self.nhead = nhead
        self.nhid = nhid
        self.init_method = init_method
        self.features_per_group = features_per_group
        self.pre_norm = pre_norm
        self.cache_trainset_representation = cache_trainset_representation
        self.cache_trainset_representation = False
        self.cached_embeddings: torch.Tensor | None = None
        self.enable_mask_feature_pred = enable_mask_feature_pred
        self.enable_mask_indicator_pred = enable_mask_indicator_pred
        self.enable_feature_attention_mask = enable_feature_attention_mask
        self.x_preprocess = x_preprocess
        self.embedding_type = embedding_type
        self.use_categorical = use_categorical
        self.classify_reg_mixed = classify_reg_mixed

        layer_creator = lambda: PerFeatureEncoderLayer(
            d_model=ninp,
            nhead=nhead,
            dim_feedforward=nhid,
            activation=activation,
            zero_init=zero_init,
            precomputed_kv=(
                precomputed_kv.pop(0) if precomputed_kv is not None else None
            ),
            pre_norm=pre_norm,
            **layer_kwargs,
        )
        self.encoder_out_norm = nn.LayerNorm(ninp, eps=1e-5, elementwise_affine=False) if pre_norm else nn.Identity()

        if repeat_same_layer:
            layer = layer_creator()
            layer_creator = lambda: layer

        nlayers_encoder = nlayers
        if use_separate_decoder and nlayers_decoder is None:
            nlayers_decoder = max((nlayers // 3) * 1, 1)
            nlayers_encoder = max((nlayers // 3) * 2, 1)

        self.transformer_encoder = LayerStack(
            layer_creator=layer_creator,
            num_layers=nlayers_encoder,
            recompute_each_layer=recompute_layer,
            min_num_layers_layer_dropout=min_num_layers_layer_dropout,
        )

        self.transformer_decoder = None
        if use_separate_decoder:
            assert nlayers_decoder is not None
            self.transformer_decoder = LayerStack(
                layer_creator=layer_creator,
                num_layers=nlayers_decoder,
            )

        self.global_att_embeddings_for_compression = None
        if use_encoder_compression_layer:
            assert use_separate_decoder
            num_global_att_tokens_for_compression = 512

            self.global_att_embeddings_for_compression = nn.Embedding(
                num_global_att_tokens_for_compression,
                ninp,
            )

            self.encoder_compression_layer = LayerStack(
                layer_creator=layer_creator,
                num_layers=2,
            )

        # initialized_decoder_dict = {}
        # for decoder_key in decoder_dict:
        #     decoder_model, decoder_n_out = decoder_dict[decoder_key]
        #     if decoder_model is None:
        #         initialized_decoder_dict[decoder_key] = nn.Sequential(
        #             nn.Linear(ninp, nhid),
        #             nn.GELU(),
        #             nn.Linear(nhid, decoder_n_out),
        #         )
        #     else:
        #         initialized_decoder_dict[decoder_key] = decoder_model(
        #             ninp,
        #             nhid,
        #             decoder_n_out,
        #         )
        # self.decoder_dict = nn.ModuleDict(initialized_decoder_dict)

        self.cls_y_decoder = nn.Sequential(
                    nn.Linear(ninp, nhid),
                    nn.GELU(),
                    nn.Linear(nhid, cls_y_num),
                    )
        
        if self.classify_reg_mixed:
            self.reg_y_decoder = nn.Sequential(
                        nn.Linear(ninp, nhid),
                        nn.LayerNorm(nhid),
                        nn.GELU(),
                        nn.Linear(nhid, 1),
                        )

        if self.enable_mask_feature_pred:
            self.feature_decoder = nn.Sequential(
                nn.Linear(ninp, nhid),
                nn.LayerNorm(nhid),
                nn.GELU(),
                nn.Linear(nhid, features_per_group),
            )

        if self.enable_mask_indicator_pred:
            self.mask_indicator_decoder = nn.Sequential(
                nn.Linear(ninp, nhid // 2),
                nn.LayerNorm(nhid // 2),
                nn.GELU(),
                nn.Linear(nhid // 2, features_per_group),
            )
        
        self._init_weights()

        self.feature_positional_embedding = feature_positional_embedding
        if feature_positional_embedding == "learned":
            self.feature_positional_embedding_embeddings = nn.Embedding(1_000, ninp)
        elif feature_positional_embedding == "subspace":
            self.feature_positional_embedding_embeddings = nn.Linear(ninp // 4, ninp)
        elif feature_positional_embedding == "subortho":
            self.feature_positional_embedding_embeddings = nn.Linear(ninp // 4, ninp)
        self.dag_pos_enc_dim = dag_pos_enc_dim
        self.cached_feature_positional_embeddings: torch.Tensor | None = None
        # self.seed = seed if seed is not None else random.randint(0, 1_000_000)  # noqa: S311


    def _init_weights(self):
        """ feature 解码器权重初始化"""
        # 特征解码器初始化
        if self.enable_mask_feature_pred:
            for module in self.feature_decoder:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    nn.init.constant_(module.bias, 0)
        
        # Mask解码器初始化
        if self.enable_mask_indicator_pred:
            for module in self.mask_indicator_decoder:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    nn.init.constant_(module.bias, 0)
            nn.init.constant_(self.mask_indicator_decoder[-1].bias, 0.0)


    def reset_save_peak_mem_factor(self, factor: int | None = None) -> None:
        """Sets the save_peak_mem_factor for all layers.

        This factor controls how much memory is saved during the forward pass
        in inference mode.

        Setting this factor > 1 will cause the model to save more memory during
        the forward pass in inference mode.

        A value of 8 is good for a 4x larger width in the fully-connected layers.
        and yields a situation were we need around
        `2*num_features*num_items*emsize*2` bytes of memory

        for a forward pass (using mixed precision).

        WARNING: It should only be used with post-norm.

        Args:
            factor: The save_peak_mem_factor to set. Recommended value is 8.
        """
        for layer in self.transformer_encoder.layers:
            assert hasattr(
                layer,
                "save_peak_mem_factor",
            ), "Layer does not have save_peak_mem_factor"
            layer.save_peak_mem_factor = factor  # type: ignore


    def process_4_x(self, data, embedding_type, seq_len, batch_size):
        x_input = data['main']
        mask = data['mask'].to(torch.bool)
        ori_shape = mask.shape
        x_input = torch.where(mask, float('nan'), x_input)
        if 'mask_embedding' != embedding_type:
            tmp_mask = mask.reshape(seq_len, batch_size, -1)
            x_input = x_input.reshape(seq_len, batch_size, -1)
            x_feature_mean = torch.nanmean(x_input, dim=(0, 1), keepdim=True)
            x_feature_mean = torch.where(torch.isnan(x_feature_mean), 0, x_feature_mean)  # 全为 nan 的特征直接用 0 替代
            x_input = torch.where(tmp_mask, x_feature_mean, x_input)
            x_input = x_input.reshape(*ori_shape)
        data['main'] = x_input
        return data
    

    def __setstate__(self, state: dict[str, Any]) -> None:
        state.setdefault("features_per_group", 1)
        state.setdefault("feature_positional_embedding", None)
        super().__setstate__(state)


    # TODO(eddiebergman): Can we just replace this with specific calls
    # such as forward, forward_with_test, forward_with_style?
    # The documentation generator complains about this function because we are
    # documenting parameters that don't exist in the signature
    def forward(self, *args: Any, **kwargs: Any) -> dict[str, torch.Tensor]:  # noqa: D417
        """Performs a forward pass through the model.

        This method supports multiple calling conventions:

        - `model((x,y), **kwargs)`
        - `model(train_x, train_y, test_x, **kwargs)`
        - `model((style,x,y), **kwargs)`

        Args:
            train_x: torch.Tensor | None
                The input data for the training set.
            train_y: torch.Tensor | None
                The target data for the training set.
            test_x: torch.Tensor | None
                The input data for the test set.
            x: torch.Tensor
                The input data.
            y: torch.Tensor | None
                The target data.
            style: torch.Tensor | None
                The style vector.
            single_eval_pos: int
                The position to evaluate at.
            only_return_standard_out: bool
                Whether to only return the standard output.
            data_dags: Any
                The data DAGs for each example.
            categorical_inds: list[int]
                The indices of categorical features.
            freeze_kv: bool
                Whether to freeze the key and value weights.

        Returns:
            The output of the model, which can be a tensor or a dictionary of tensors.
        """
        half_layers = kwargs.pop("half_layers", False)
        assert half_layers is False

        supported_kwargs = {
            "only_return_standard_out",
            "style",
            "y_type",
            "data_dags",
            "categorical_inds",
            "freeze_kv",
            "train_x",
            "train_y",
            "test_x",
            "single_eval_pos",
        }
        spurious_kwargs = set(kwargs.keys()) - supported_kwargs
        assert not spurious_kwargs, spurious_kwargs
        if 3 == len(args[0]):
            style_src, x_src, y_src = args[0]
            args = [x_src, y_src]
        elif 4 == len(args[0]):
            style_src, x_src, y_src, y_type = args[0]
            args = [style_src, x_src, y_src, y_type]
        elif 5 == len(args[0]):
            style_src, x_src, y_src, y_type, x_mask = args[0]
            args = [style_src, x_src, y_src, y_type, x_mask]

        if args == () and all(k in kwargs for k in ("train_x", "train_y", "test_x")):
            print('--> there?')
            assert "single_eval_pos" not in kwargs
            x = kwargs.pop("train_x")
            train_y = kwargs.pop("train_y")
            test_x = kwargs.pop("test_x")
            if test_x is not None:
                x = torch.cat((x, test_x), dim=0)
            return self._forward(x, train_y, single_eval_pos=len(train_y), **kwargs)

        if len(args) == 2:
            # current code is using this only
            x, y = args
            return self._forward(x, y, **kwargs)

        if len(args) == 3:
            style, x, y = args
            return self._forward(x, y, style=style, **kwargs)
        
        if 4 == len(args):
            style, x, y, y_type = args
            return self._forward(x, y, y_type=y_type, style=style, **kwargs)
        elif 5 == len(args):
            style, x, y, y_type, x_mask = args
            if isinstance(x, dict):
                device = x['main'].device
            else:
                device = x.device
            x_mask = x_mask.float().to(device)
            return self._forward(x, y, x_mask, y_type=y_type, style=style, **kwargs)

        raise ValueError("Unrecognized input. Please follow the doc string.")


    def _forward(  # noqa: PLR0912, C901
        self,
        x: torch.Tensor | dict,
        # TODO(eddiebergman): Not sure if it can be None but the function seems to
        # indicate it could
        y: torch.Tensor | dict | None,
        x_mask: torch.Tensor = None,
        *,
        y_type: torch.Tensor = None,
        single_eval_pos: int | None = None,
        only_return_standard_out: bool = True,
        style: torch.Tensor | None = None,
        data_dags: list[Any] | None = None,
        categorical_inds: list[int] | None = None,
        half_layers: bool = False,
    ) -> Any | dict[str, torch.Tensor]:
        """The core forward pass of the model.

        Args:
            x: The input data. Shape: `(seq_len, batch_size, num_features)`
            y: The target data. Shape: `(seq_len, batch_size)`
            single_eval_pos:
                The position to evaluate at. If `None`, evaluate at all positions.
            only_return_standard_out: Whether to only return the standard output.
            style: The style vector.
            data_dags: The data DAGs for each example in the batch.
            categorical_inds: The indices of categorical features.
            half_layers: Whether to use half the layers.

        Returns:
            A dictionary of output tensors.
        """
        assert style is None
        if self.cache_trainset_representation:
            if not single_eval_pos:  # none or 0
                assert y is None
        else:
            assert y is not None
            assert single_eval_pos
        
        single_eval_pos_ = single_eval_pos or 0
        if x_mask is None:  # 根据 x 直接构造 x_mask
            x_mask = torch.isnan(x).to(torch.int32).to(x.device)

        if isinstance(x, dict):
            assert "main" in set(x.keys()), f"Main must be in input keys: {x.keys()}."
        else:
            x = {"main": x, "mask": x_mask}
        seq_len, batch_size, num_features = x["main"].shape

        # get categorical mask, shape: (batch_size, num_features)
        if self.use_categorical:
            categorical_mask = detect_categorical_features(x["main"])
        else:
            categorical_mask = None

        if y is None:
            # TODO: check dtype.
            y = torch.zeros(
                0,
                batch_size,
                device=x["main"].device,
                dtype=x["main"].dtype,
            )

        if isinstance(y, dict):
            assert "main" in set(y.keys()), f"Main must be in input keys: {y.keys()}."
        else:
            y = {"main": y}

        for k in x:
            num_features_ = x[k].shape[2]
            # pad to multiple of features_per_group
            missing_to_next = (
                self.features_per_group - (num_features_ % self.features_per_group)
            ) % self.features_per_group

            if missing_to_next > 0:
                x[k] = torch.cat(
                    (
                        x[k],
                        torch.zeros(
                            seq_len,
                            batch_size,
                            missing_to_next,
                            device=x[k].device,
                            dtype=x[k].dtype,
                        ),
                    ),
                    dim=-1,
                )

        # Splits up the input into subgroups
        for k in x:
            x[k] = einops.rearrange(
                x[k],
                "s b (f n) -> b s f n",
                n=self.features_per_group,
            )  # s b f -> b s #groups #features_per_group

        for k in y:
            if y[k].ndim == 1:
                y[k] = y[k].unsqueeze(-1)
            if y[k].ndim == 2:
                y[k] = y[k].unsqueeze(-1)  # s b -> s b 1

            y[k] = y[k].transpose(0, 1)  # s b 1 -> b s 1

            if y[k].shape[1] < x["main"].shape[1]:
                assert (
                    y[k].shape[1] == single_eval_pos_
                    or y[k].shape[1] == x["main"].shape[1]
                )
                assert k != "main" or y[k].shape[1] == single_eval_pos_, (
                    "For main y, y must not be given for target"
                    " time steps (Otherwise the solution is leaked)."
                )
                if y[k].shape[1] == single_eval_pos_:
                    y[k] = torch.cat(
                        (
                            y[k],
                            torch.nan
                            * torch.zeros(
                                y[k].shape[0],
                                x["main"].shape[1] - y[k].shape[1],
                                y[k].shape[2],
                                device=y[k].device,
                                dtype=y[k].dtype,
                            ),
                        ),
                        dim=1,
                    )

            y[k] = y[k].transpose(0, 1)  # b s 1 -> s b 1

        # making sure no label leakage ever happens
        y["main"][single_eval_pos_:] = torch.nan
        return_cls_y = False
        if isinstance(y_type, int):
            if 0 == y_type:
                y_type = torch.zeros_like(y['main'], device=y['main'].device)
                return_cls_y = True  # 只返回分类结果
            elif 1 == y_type:
                y_type = torch.ones_like(y['main'], device=y['main'].device)
                return_cls_y = False  # 只返回回归结果
        y_type = y_type.reshape(y["main"].shape)

        if self.classify_reg_mixed:
            embedded_y = self.y_embedding(y, 
                                          y_type, 
                                          single_eval_pos=single_eval_pos_,
                                          cache_trainset_representation=self.cache_trainset_representation,
                                          ).transpose(0, 1)
        else:
            embedded_y = self.cls_y_encoder(
                y,
                single_eval_pos=single_eval_pos_,
                cache_trainset_representation=self.cache_trainset_representation,
            ).transpose(0, 1) # type: ignore

        if torch.isnan(embedded_y).any():
            raise ValueError(
                f"{torch.isnan(embedded_y).any()=}, make sure to add nan handlers"
                " to the ys that are not fully provided (test set missing)",
            )
        del y

        batch_size, seq_len, feature_group_num, feature_group_size = x['main'].shape
        for k in x:
            x[k] = einops.rearrange(x[k], "b s f n -> s (b f) n")
        
        extra_encoders_args = {}
        # We have to re-work categoricals based on the subgroup they fall into.
        if self.use_categorical:
            assert self.features_per_group == 1, "features_per_group must be 1 when using categorical features"
            categorical_mask = einops.rearrange(categorical_mask, "b f -> (b f)")
            extra_encoders_args["categorical_mask"] = categorical_mask

        preprocessed_x = self.x_preprocess(x, single_eval_pos=single_eval_pos)
        real_x = preprocessed_x['main'].clone().detach()
        real_x = einops.rearrange(real_x, "s (b f) n -> b s f n", b=batch_size, f=feature_group_num)
        preprocessed_x = self.process_4_x(preprocessed_x, self.embedding_type, seq_len, batch_size)

        x_encoder_result = self.encoder(
            preprocessed_x,
            single_eval_pos=single_eval_pos_,
            cache_trainset_representation=self.cache_trainset_representation,
            **extra_encoders_args,
            )
        x_emb_result = x_encoder_result['output']
        
        embedded_x = einops.rearrange(
            x_emb_result,
            "s (b f) e -> b s f e",
            b=embedded_y.shape[0],
        )  # b s f 1 -> b s f e
        del x

        embedded_x, embedded_y = self.add_embeddings(
            embedded_x,
            embedded_y,
            data_dags=data_dags,
            num_features=num_features,
            seq_len=seq_len,
            cache_embeddings=(
                self.cache_trainset_representation and single_eval_pos is not None
            ),
            use_cached_embeddings=(
                self.cache_trainset_representation and single_eval_pos is None
            ),
        )
        del data_dags

        # b s f e + b s 1 e -> b s f+1 e
        embedded_input = torch.cat((embedded_x, embedded_y.unsqueeze(2)), dim=2)

        if torch.isnan(embedded_input).any():
            raise ValueError(
                f"There should be no NaNs in the encoded x and y."
                "Check that you do not feed NaNs or use a NaN-handling enocder."
                "Your embedded x and y returned the following:"
                f"{torch.isnan(embedded_x).any()=} | {torch.isnan(embedded_y).any()=}",
            )
        del embedded_y, embedded_x

        if self.enable_feature_attention_mask:
            # 在 x 的 mask 张量后拼接一个 0 值，表示所有元素都要对 y 进行 attention
            feature_attention_mask = torch.cat([x_mask, torch.zeros(*x_mask.shape[:-1], 1, device=x_mask.device)], dim=-1)
            feature_attention_mask = feature_attention_mask.transpose(0, 1)
        else:
            feature_attention_mask = None  # 默认不使用 feature mask attention

        encoder_out = self.transformer_encoder(
            (
                embedded_input
                if not self.transformer_decoder
                else embedded_input[:, :single_eval_pos_]
            ),
            feature_attention_mask=feature_attention_mask,
            single_eval_pos=single_eval_pos,
            half_layers=half_layers,
            cache_trainset_representation=self.cache_trainset_representation,
        )  # b s f+1 e -> b s f+1 e

        encoder_out = self.encoder_out_norm(encoder_out)

        # If we are using a decoder
        if self.transformer_decoder:
            assert not half_layers
            assert encoder_out.shape[1] == single_eval_pos_

            if self.global_att_embeddings_for_compression is not None:
                # TODO: fixed number of compression tokens
                train_encoder_out = self.encoder_compression_layer(
                    self.global_att_embeddings_for_compression,
                    att_src=encoder_out[:, single_eval_pos_],
                    single_eval_pos=single_eval_pos_,
                )

            test_encoder_out = self.transformer_decoder(
                embedded_input[:, single_eval_pos_:],
                single_eval_pos=0,
                att_src=encoder_out,
            )
            encoder_out = torch.cat([encoder_out, test_encoder_out], 1)
            del test_encoder_out

        del embedded_input

        # out: s b e
        test_encoder_out = encoder_out[:, single_eval_pos_:, -1].transpose(0, 1)
        test_y_type = y_type[single_eval_pos_:]
        encoder_out_4_feature = encoder_out[:, :, :-1, :]  # b s f e

        if self.enable_mask_feature_pred or self.enable_mask_indicator_pred:
            cls_output, reg_output = self.y_decoder(test_encoder_out, test_y_type)
            feature_pred, mask_logits = self.mask_feature_decoder(encoder_out_4_feature)
            output_decoded = {
                "cls_output": cls_output,
                "reg_output": reg_output,
                "feature_pred": feature_pred,
                "mask_indicator_pred": mask_logits,
                "real_x": real_x,
                "process_config": {
                    "n_x_padding": missing_to_next,
                    "features_per_group": self.x_preprocess[4].num_features,
                    "num_used_features": self.x_preprocess[4].number_of_used_features_,
                    "mean_for_normalization": self.x_preprocess[3].mean_for_normalization,
                    "std_for_normalization": self.x_preprocess[3].std_for_normalization
                }
            }
        elif self.classify_reg_mixed:
            cls_output, reg_output = self.y_decoder(test_encoder_out, test_y_type)
            if return_cls_y:
                output_decoded = cls_output
            else:
                output_decoded = reg_output
        elif self.classify_reg_mixed == False and return_cls_y:
            cls_output, reg_output = self.y_decoder(test_encoder_out, test_y_type)
            output_decoded = cls_output
        elif only_return_standard_out:
            output_decoded = self.decoder_dict["standard"](test_encoder_out)
        else:
            output_decoded = (
                {k: v(test_encoder_out) for k, v in self.decoder_dict.items()}
                if self.decoder_dict is not None
                else {}
            )
            # out: s b e
            train_encoder_out = encoder_out[:, :single_eval_pos_, -1].transpose(0, 1)
            output_decoded["train_embeddings"] = train_encoder_out
            output_decoded["test_embeddings"] = test_encoder_out

        return output_decoded


    def mask_feature_decoder(self, x_encoding):
        feature_pred, mask_logits = None, None
        if self.enable_mask_feature_pred:
            feature_pred = self.feature_decoder(x_encoding)
        if self.enable_mask_indicator_pred:
            mask_logits = self.mask_indicator_decoder(x_encoding)
        return feature_pred, mask_logits


    def y_decoder(self, test_encoder_out, test_y_type):
        if not self.classify_reg_mixed:
            cls_y = self.cls_y_decoder(test_encoder_out)
            reg_y = None
            return cls_y, reg_y
        
        seq_len, batch_size, emb_size = test_encoder_out.shape
        flat_test_encoder_out = test_encoder_out.reshape(-1, emb_size)
        flat_test_y_type = test_y_type.reshape(-1)
        
        idx = torch.arange(len(flat_test_encoder_out), device=flat_test_encoder_out.device)
        idx_cls = idx[flat_test_y_type == 0]
        idx_reg = idx[flat_test_y_type == 1]

        cls_y_encoder_out = flat_test_encoder_out[idx_cls]
        reg_y_encoder_out = flat_test_encoder_out[idx_reg]
        cls_y_encoder_out = cls_y_encoder_out.reshape(seq_len, -1, emb_size)
        reg_y_encoder_out = reg_y_encoder_out.reshape(seq_len, -1, emb_size)

        cls_y = self.cls_y_decoder(cls_y_encoder_out)
        reg_y = self.reg_y_decoder(reg_y_encoder_out)

        return cls_y, reg_y


    def y_embedding(self, y, y_type, single_eval_pos, cache_trainset_representation):
        y = y['main']
        seq_len, batch_size, y_num = y.shape
        y_flat = y.reshape(-1)
        y_type_flat = y_type.reshape(-1)
        
        idx = torch.arange(len(y_flat), device=y.device)
        idx_cls = idx[y_type_flat == 0]
        idx_reg = idx[y_type_flat == 1]
        y_cls = y_flat[idx_cls]
        y_reg = y_flat[idx_reg]

        y_cls = y_cls.reshape(seq_len, -1, y_num)
        y_reg = y_reg.reshape(seq_len, -1, y_num)
        y_cls = {'main': y_cls}
        y_reg = {'main': y_reg}

        cls_y_emb = self.cls_y_encoder(y_cls, single_eval_pos=single_eval_pos, cache_trainset_representation=cache_trainset_representation) if len(idx_cls) > 0 else None
        reg_y_emb = self.reg_y_encoder(y_reg, single_eval_pos=single_eval_pos, cache_trainset_representation=cache_trainset_representation) if len(idx_reg) > 0 else None
        
        emb_size = self.ninp
        out = torch.empty(len(y_flat), emb_size, dtype=torch.float16, device=y_flat.device)
        if cls_y_emb is not None:            
            cls_y_emb_flat = cls_y_emb.reshape(-1, emb_size)
            out.index_put_((idx_cls,), cls_y_emb_flat)

        if reg_y_emb is not None:
            reg_y_emb_flat = reg_y_emb.reshape(-1, emb_size).to(torch.float16)
            out.index_put_((idx_reg,), reg_y_emb_flat)

        output = out.reshape(seq_len, batch_size, emb_size)
        return output


    def add_embeddings(  # noqa: C901, PLR0912
        self,
        x: torch.Tensor,
        y: torch.Tensor,
        *,
        data_dags: Iterable[nx.DiGraph] | None,
        num_features: int,
        seq_len: int,
        cache_embeddings: bool = False,
        use_cached_embeddings: bool = False,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        if use_cached_embeddings and self.cached_embeddings is not None:
            assert (
                data_dags is None
            ), "Caching embeddings is not supported with data_dags at this point."
            x += self.cached_embeddings[None, None]
            return x, y

        if self.feature_positional_embedding == "normal_rand_vec":
            embs = torch.randn(
                (x.shape[2], x.shape[3]),
                device=x.device,
                dtype=x.dtype,
            )
            x += embs[None, None]
        elif self.feature_positional_embedding == "uni_rand_vec":
            embs = (
                torch.rand(
                    (x.shape[2], x.shape[3]),
                    device=x.device,
                    dtype=x.dtype,
                )
                * 2
                - 1
            )
            x += embs[None, None]
        elif self.feature_positional_embedding == "learned":
            w = self.feature_positional_embedding_embeddings.weight
            embs = w[
                torch.randint(
                    0,
                    w.shape[0],
                    (x.shape[2],),
                )
            ]
            x += embs[None, None]
        elif self.feature_positional_embedding == "subspace":
            embs = torch.randn(
                (x.shape[2], x.shape[3] // 4),
                device=x.device,
                dtype=x.dtype,
            )
            embs = self.feature_positional_embedding_embeddings(embs)
            x += embs[None, None]
        elif self.feature_positional_embedding == "orthogonal":
            with torch.cuda.amp.autocast(enabled=False):
                embs = torch.empty(
                    (x.shape[2], x.shape[3]),
                    device=x.device,
                    dtype=torch.float32,
                )
                torch.nn.init.orthogonal_(embs)
            x += embs[None, None].to(x.dtype)

        elif self.feature_positional_embedding == "subortho":
            with torch.cuda.amp.autocast(enabled=False):
                embs = torch.randn(
                    (x.shape[2], x.shape[3] // 4),
                    device=x.device,
                    dtype=torch.float32,
                )
                torch.nn.init.orthogonal_(embs)
            embs =self.feature_positional_embedding_embeddings(embs.to(x.dtype))
            x += embs[None, None]
        elif self.feature_positional_embedding is None:
            embs = None
        else:
            raise ValueError(f"Unknown {self.feature_positional_embedding=}")

        self.cached_embeddings = None
        if cache_embeddings and embs is not None:
            assert (
                data_dags is None
            ), "Caching embeddings is not supported with data_dags at this point."
            self.cached_embeddings = embs

        # TODO(old) should this go into encoder?
        # could also be made a bit more concise by moving down to operate on full_x
        if data_dags is not None:
            for b_i, data_dag in enumerate(data_dags):
                # TODO(eddibergman): Very inneficient way to make a full connect
                # DiGraph
                g_: nx.DiGraph = data_dag.copy()
                while _networkx_add_direct_connections(g_):
                    pass

                subgraph: nx.DiGraph = g_.subgraph(  # type: ignore
                    [
                        n
                        for n, info in g_.nodes.items()
                        if (info["is_feature"] or info["is_target"])
                    ],
                )
                k = self.dag_pos_enc_dim
                assert k > 0
                _add_pos_emb(subgraph, k=k)

                graph_pos_embs_features = torch.zeros((num_features, k))
                graph_pos_embs_targets = torch.zeros((1, k))  # shape: (num_targets, k)

                for node_info in subgraph.nodes.values():
                    for feature_idx in node_info.get("feature_idxs", []):
                        graph_pos_embs_features[feature_idx] = node_info[
                            "positional_encoding"
                        ]
                    for target_idx in node_info.get("target_idxs", []):
                        graph_pos_embs_targets[target_idx] = node_info[
                            "positional_encoding"
                        ]

                graph_pos_embs_targets -= graph_pos_embs_features.mean(0, keepdim=True)
                graph_pos_embs_features -= graph_pos_embs_features.mean(0, keepdim=True)

                graph_pos_embs_features = graph_pos_embs_features[None].expand(
                    seq_len,
                    -1,
                    -1,
                )
                x[b_i, :, :, :k] += graph_pos_embs_features.to(y.device, y.dtype)

                graph_pos_embs_targets = (
                    graph_pos_embs_targets[None].expand(seq_len, -1, -1).squeeze(-2)
                )
                y[b_i, :, :k] += graph_pos_embs_targets.to(y.device, y.dtype)
        else:
            assert not hasattr(self, "dag_pos_enc_dim") or not self.dag_pos_enc_dim

        return x, y

    def empty_trainset_representation_cache(self) -> None:
        for layer in (self.transformer_decoder or self.transformer_encoder).layers:
            layer.empty_trainset_representation_cache()


def _networkx_add_direct_connections(graph: nx.DiGraph) -> bool:
    added_connection = False
    # Get the list of nodes
    nodes = list(graph.nodes)

    # Iterate over each node
    for node in nodes:
        # Get the direct neighbors of the current node
        neighbors = list(graph.neighbors(node))

        # Iterate over the neighbors of the current node
        for neighbor in neighbors:
            # Get the neighbors of the neighbor
            second_neighbors = list(graph.neighbors(neighbor))

            # Iterate over the neighbors of the neighbor
            for second_neighbor in second_neighbors:
                # Add a direct edge from the current node to the second neighbor,
                # if it doesn't exist already
                if second_neighbor not in graph.neighbors(node):
                    graph.add_edge(node, second_neighbor)

                    added_connection = True
    return added_connection


def _add_pos_emb(
    graph: nx.DiGraph,
    *,
    is_undirected: bool = False,
    k: int = 20,
) -> None:
    from scipy.sparse.linalg import eigs, eigsh

    eig_fn = eigs if not is_undirected else eigsh

    L = nx.directed_laplacian_matrix(graph)
    np.nan_to_num(L, nan=0.0, copy=False)

    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        eig_vals, eig_vecs = eig_fn(  # type: ignore
            L,
            k=k + 1,
            which="SR" if not is_undirected else "SA",
            return_eigenvectors=True,
        )

        eig_vecs = np.real(eig_vecs[:, eig_vals.argsort()])
        pe_ = torch.from_numpy(eig_vecs[:, 1 : k + 1])
        pe = torch.zeros(len(eig_vecs), k)
        pe[:, : pe_.shape[1]] = pe_
        sign = -1 + 2 * torch.randint(0, 2, (k,))
        pe *= sign

        # TODO(old) Double check the ordering is right
        for n, pe_ in zip(graph.nodes(), pe):
            graph.nodes[n]["positional_encoding"] = pe_
