import os
import math
import torch
import argparse
import pandas as pd
import numpy as np

from tqdm import tqdm
from pathlib import Path
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from inference.predictor import LDMPredictor
from inference_benchmark160 import get_categorical_features_indices, mask_prediction_eval, BasicImpute, gen_nan

if not torch.cuda.is_available():
    raise SystemError('GPU device not found. For fast training, please enable GPU.')


def model_path_save(model_path,args,model_save):
    if model_path != 'auto':
        model_path = Path(model_path)
        if not model_path.exists():
            raise ValueError(f"Invalid path: {model_path} does not exist!")
        
        # 模型预处理
        model_default = args.model_default
        model_m = torch.load(open(model_path, 'rb'))   
        model_d = torch.load(open(model_default,'rb')) 
        my_stat = model_m[0]    
        new_stat = {k.replace('module.', ''): v for k, v in my_stat.items()}    
        for k in model_d['state_dict'].keys():
            if k in new_stat:
                model_d['state_dict'][k] = new_stat[k]
            else:
                print(f"[!] Warning: {k} not found in new model params")
                
        # 保存融合后的模型
        torch.save(model_d, open(model_save, 'wb'))


def process_dataset(X_train, X_test, y_train, y_test, model_path, gpu_id, mask_prediction, drop_percentage):
    """
    处理数据集：训练 TabPFN 和其他模型，计算 RMSE 和 R²，并保存结果
    """
    # 记录数据集大小和特征数
    sample_size, feature_count = X_train.shape
    rmse_results = {"Sample_Size": sample_size, "Feature_Count": feature_count}
    r2_results = {}

    y_mean = y_train.mean()
    y_std = y_train.std()
    y_train_normalized = (y_train - y_mean) / y_std
    y_test_normalized = (y_test - y_mean) / y_std
    
    # 初始化回归模型
    data_device = f'cuda:{gpu_id}'
    model = LDMPredictor(device=torch.device(data_device), n_estimators=8, model_path=model_path, task_type='Regression', mask_prediction=mask_prediction)

    # 训练模型
    # model.fit(X_train, y_train_normalized)
    if not args.mask_prediction:    
        y_pred, _ = model.predict(X_train, y_train_normalized, X_test)
        mask_pred_results = None
    else:
        # 对每一列进行检查
        for col in X_train.columns:
            if X_train[col].dtype == 'object':  # 检查是否是字符串列
                try:
                    le = LabelEncoder()
                    X_train[col] = le.fit_transform(X_train[col])
                    X_test[col] = le.transform(X_test[col])  # 确保测试集使用相同的编码
                except Exception as e:
                    X_train = X_train.drop(columns=[col])
                    X_test = X_test.drop(columns=[col])
        scaler = MinMaxScaler()
        X_train = pd.DataFrame(scaler.fit_transform(X_train)) if isinstance(X_train, pd.DataFrame) else scaler.fit_transform(X_train)
        X_test = pd.DataFrame(scaler.transform(X_test)) if isinstance(X_test, pd.DataFrame) else scaler.transform(X_test)
        trainX, trainy = X_train, y_train_normalized
        trainX = np.asarray(trainX, dtype=np.float32)
        trainy = np.asarray(trainy, dtype=np.float32)
        testX, testy = X_test, y_test_normalized
        testX = np.asarray(testX, dtype=np.float32)
        testy = np.asarray(testy, dtype=np.float32)
                    
        # 产生缺失值
        if drop_percentage > 0:
            categories = get_categorical_features_indices(trainX)
            categorical_indices = list(categories.keys())
            cont_indices = [idx for idx in range(testX.shape[1]) if idx not in categories]
            print("trainX.shape:", trainX.shape)
            print("testX.shape:", testX.shape)
            print("Categorical indices:", categorical_indices)
            print("Cont indices:", cont_indices)
            testX, testX_original, nan_mask = gen_nan(testX, drop_percentage)     
            # print(testX)
            # print(testX_original)
            # print(nan_mask)
            # print(testX.dtypes)
            mask_pred_results = dict()
            y_pred, mask_prediction_ = model.predict(trainX, trainy, testX)   
            trainX = trainX.values if isinstance(trainX, pd.DataFrame) else trainX
            testX = testX.values if isinstance(testX, pd.DataFrame) else testX
            testX_original = testX_original.values if isinstance(testX_original, pd.DataFrame) else testX_original
            mask_prediction_ = mask_prediction_[-testX.shape[0]:].astype(float)
            print("-"*30+"PFN"+"-"*30)
            mask_pred_cls_error, mask_pred_reg_error = mask_prediction_eval(mask_prediction_, testX_original, nan_mask, categories) 
            mask_pred_results['mask_pred_cls_error_PFN'] = mask_pred_cls_error
            mask_pred_results['mask_pred_reg_error_PFN'] = mask_pred_reg_error                    
            BasicImpute(trainX, testX, testX_original, cont_indices, categorical_indices, categories, nan_mask, mask_pred_results)
        else:
            mask_pred_results = dict()
            y_pred, _ = model.predict(trainX, trainy, testX)   
    # 计算 RMSE 和 R²
    y_pred = y_pred.to('cpu')
    mse = mean_squared_error(y_test_normalized, y_pred)
    rmse = math.sqrt(mse)
    # rmse2 = mean_squared_error(y_test_normalized, y_pred, squared=False)
    r2 = r2_score(y_test_normalized, y_pred)

    r2_results[f"R2"] = r2
    rmse_results["rmse"] = rmse

    return rmse_results, r2_results, mask_pred_results


def load_data(data_path):
    data = pd.read_csv(data_path)
    X = data.iloc[:, :-1]
    y = data.iloc[:, -1].astype(float)
    return X, y


if __name__ == '__main__':
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='运行TabPFN评估')
    parser.add_argument('--model_path', type=str, required=True, help='输入模型文件路径，例如 /path/to/model.cpkt')
    parser.add_argument('--gpuID', type=int, default=0, help='指定使用的GPU ID，默认为0')
    parser.add_argument('--mask_prediction', action="store_true", help='是否使用mask prediction')
    parser.add_argument('--drop_percentage', type=float, default=0.0, help='在X中随机产生nan的比例')
    parser.add_argument('--data_dir', type=str, default='./data', help='指定cc18数据集本地存储目录，默认为./data')
    args = parser.parse_args()

    model_path = args.model_path
    data_dir = args.data_dir
    gpu_id = int(args.gpuID) % 8  # 从命令行参数获取GPU ID
    benchmark_name = Path(data_dir).name
    # 动态生成保存路径
    kaggle_results_path = model_path.replace('.cpkt', '_benchmark_results')
    save_root_dir = model_path.split('/')[-3]
    save_root = kaggle_results_path.replace(save_root_dir, save_root_dir+f'_{benchmark_name}')
    save_root_pred = save_root.replace(save_root_dir, save_root_dir+f'_pred')
    os.makedirs(save_root, exist_ok=True)
    os.makedirs(save_root_pred, exist_ok=True)
    # 新增检查：如果结果文件存在则提前退出
    result_file = os.path.join(save_root, 'all_rst.csv')
    if os.path.exists(result_file):
        mfp_path = model_path.replace('.cpkt', '-mfp.cpkt')
        if not os.path.exists(mfp_path):
            open(mfp_path, 'w').close()  # 创建空文件
        print(f"结果文件已存在，跳过执行: {result_file}")
        exit(0)

    # 获取所有数据集的汇总结果（包含V2的评估指标）
    summary_file_path = Path(data_dir, f'{benchmark_name}.csv')
    summary_pd_data = pd.read_csv(summary_file_path)
    summary_data = {}
    columns = summary_pd_data.columns
    for _, row in summary_pd_data.iterrows():
        folder = row['folder']
        single_dataset_data = {}
        for column in columns:
            single_dataset_data[column] = row[column]
        summary_data[folder] = single_dataset_data

    summary_results = []  # 记录所有数据集的评测结果
    # 依次获取模型在每个数据集上的从评测结果
    for idx, dataset_name in tqdm(enumerate(summary_data)):
        try:
        # if True:
            train_data_path = Path(data_dir, dataset_name, f'{dataset_name}_train.csv')
            test_data_path = Path(data_dir, dataset_name, f'{dataset_name}_test.csv')
            X_train, y_train = train_data = load_data(train_data_path)
            X_test, y_test = test_data = load_data(test_data_path)

            # 处理并计算 RMSE 和 R²
            rmse_results, r2_results, mask_pred_results = process_dataset(X_train, X_test, y_train, y_test, model_path, gpu_id, args.mask_prediction, args.drop_percentage)
                
            print(f"[{idx}] -> {rmse_results}, {r2_results}")
            single_data_result = summary_data[dataset_name].copy()
            single_data_result.update(**rmse_results)
            single_data_result.update(**r2_results)
            if mask_pred_results is not None:
                single_data_result.update(**mask_pred_results)
            summary_results.append(single_data_result)
        except Exception as e:
            # raise e
            print(f"Error processing {dataset_name}: {e}")

    if len(summary_results):
        summary_results_df = pd.DataFrame(summary_results)
        summary_results_df.to_csv(os.path.join(save_root, 'all_rst.csv'), index=False)
        summary_results_df.to_csv(os.path.join(save_root_pred, 'all_rst.csv'), index=False)

