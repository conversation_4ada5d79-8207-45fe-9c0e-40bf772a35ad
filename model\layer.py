from typing import Callable, Literal, Optional
import functools

import torch
import torch.nn.functional as F
import torch.nn as nn
from torch.utils.checkpoint import checkpoint
try:
    from flash_attn.flash_attn_interface import flash_attn_varlen_kvpacked_func, flash_attn_varlen_qkvpacked_func
    HAVE_FLASH_ATTN = True
except (ModuleNotFoundError, ImportError):
    HAVE_FLASH_ATTN = False
    
from functools import partial
from typing_extensions import override

Activation = Literal['gelu', 'relu']

ACTIVATION_FN: dict[str, Callable[[torch.Tensor], torch.Tensor]] = {
    'gelu': nn.GELU(), 
    'relu': nn.ReLU(),
}

class LayerNormMixedPrecision(nn.LayerNorm):
    """
    When the embedding dimension is below 512, use half precision for computation to improve performance. 
    If the embedding dimension exceeds 512, it may cause training instability.
    """
    def forward(self, input: torch.Tensor):
        if input.dtype == torch.float16 and sum(self.normalized_shape) < 512:
            with torch.amp.autocast("cuda" if input.is_cuda else "cpu", enabled=False):
                return super().forward(input)
        else:
            return super().forward(input)

class MLP(torch.nn.Module):
    """Multi-Layer Perceptron"""
    def __init__(self, 
                 in_features: int, 
                 hidden_size:int, 
                 out_features: int, 
                 has_bias:bool, 
                 device: torch.device | None, 
                 dtype: torch.dtype | None,  
                 activation: Activation = 'gelu', 
                 depth:int=2, 
                 init_std:float=0):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.activation = activation
        self.layers = []
       
        if depth == 1:
            self.layers.append(nn.Linear(in_features, out_features, bias=has_bias, device=device, dtype=dtype))
        else:
             # input layer
            self.layers.append(nn.Linear(in_features, hidden_size, bias=has_bias, device=device, dtype=dtype))
            self.layers.append(ACTIVATION_FN[self.activation])
            # hidden layers
            for i in range(depth - 2):
                self.layers.append(nn.Linear(hidden_size, hidden_size, bias=has_bias, device=device, dtype=dtype))
                self.layers.append(ACTIVATION_FN[self.activation])
            # output layer
            self.layers.append(nn.Linear(hidden_size, out_features, bias=has_bias, device=device, dtype=dtype))
            torch.nn.init.normal_(self.layers[-1].weight, mean=0, std=init_std)
        self.mlp = nn.Sequential(*self.layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.mlp(x)

class MultiheadAttention(torch.nn.Module):
    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        device: Optional[torch.device] = None,
        dtype: Optional[torch.dtype] = None,
        qkv_combined: bool = True,
        qkv_w_init_method: Literal['xavier_uniform', 'kaiming_uniform'] = 'xavier_uniform',
        dropout:float=0,
        recompute:bool=False,
        mlp_init_std:float=0.0,
    ):
        super().__init__()
        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.qkv_combined = qkv_combined
        # self.copy_first_head = copy_first_head
        self.qkv_w_init_method = qkv_w_init_method
        self.dropout = dropout
        self.recompute = recompute
        self.device = device
        self.dtype = dtype
        self.mlp_init_std = mlp_init_std

        self.out_proj_weight = torch.nn.Parameter(torch.empty(self.num_heads, self.head_dim, self.embed_dim, device=self.device, dtype=self.dtype))
        self.qkv_proj_weight = torch.nn.Parameter(torch.empty(3, self.num_heads, self.head_dim, self.embed_dim, device=device, dtype=dtype))

        torch.nn.init.normal_(self.out_proj_weight, mean=0, std=self.mlp_init_std)
        if self.qkv_w_init_method == 'xavier_uniform':
            nn.init.xavier_uniform_(self.qkv_proj_weight)
        elif self.qkv_w_init_method == 'kaiming_uniform':
            nn.init.kaiming_uniform_(self.qkv_proj_weight)
        else:
            raise ValueError(f"Unsupported initialization method, supports Kaiming and Xavier initialization.")
        
        self.q_proj_weight = self.qkv_proj_weight[0]
        self.kv_proj_weight = self.qkv_proj_weight[1:]
        
        if recompute:
            self.forward = partial(checkpoint, self.forward, use_reentrant=False)  # type: ignore
    
    def get_cu_seqlens(self, batch_size: int, seqlen: int, device: torch.device) -> torch.Tensor:
        return torch.arange(
            0,
            (batch_size + 1) * seqlen,
            step=seqlen,
            dtype=torch.int32,
            device=device,
        )
    
    def compute_attention_by_torch(self, qkv:torch.Tensor|None, q:torch.Tensor|None, kv:torch.Tensor|None, attn_mask:torch.Tensor|None) -> torch.Tensor:
        '''Since flash attention does not support attn_mask, use scaled_dot_product_attention to compute attention when attn_mask is not None'''
        if qkv is not None:
            q, k, v = qkv.unbind(dim=-3)
        elif kv is not None and q is not None:
            k,v = kv.unbind(dim=-3)
        else:
            raise ValueError("When qkv is None, q and kv cannot both be None at the same time")
        assert q is not None and k is not None and v is not None, "q, k, and v must not be None"
        
        attention_outputs = torch.nn.functional.scaled_dot_product_attention(
                q.transpose(1, 2),
                k.transpose(1, 2),
                v.transpose(1, 2),
                attn_mask=attn_mask,
                dropout_p=self.dropout_p,
            )
        attention_outputs = attention_outputs.transpose(1, 2)
        return attention_outputs
    
    def compute_attention_by_flashattn(self, qkv:torch.Tensor|None, q:torch.Tensor|None, kv:torch.Tensor|None) -> torch.Tensor:
        "Compute attention using flash attention"
        assert HAVE_FLASH_ATTN, "Flash attention is not supported. Please install/reinstall flash attention."
        if self.qkv_combined and qkv is not None:
            B,S = qkv.shape[:2]
            atten_out = flash_attn_varlen_qkvpacked_func( # type: ignore
                        qkv.reshape(B * S, 3, self.num_heads, self.head_dim),
                        self.get_cu_seqlens(B, S, qkv.device),
                        S,
                        dropout_p=self.dropout,
                        softmax_scale=None,
                        causal=False,
                        return_attn_probs=False,
                        deterministic=False,
                    )
        elif not self.qkv_combined and q is not None and kv is not None:
            B,S = q.shape[:2]
            kv_shape = kv.shape
            atten_out = flash_attn_varlen_kvpacked_func( # type: ignore
                    q.reshape(B * S, self.num_heads, self.head_dim),
                    kv.reshape(B * kv_shape[1], 2, self.num_heads, self.head_dim),
                    self.get_cu_seqlens(B, S, q.device),
                    self.get_cu_seqlens(B, kv_shape[1], kv.device),
                    B,
                    kv_shape[1],
                    dropout_p=self.dropout,
                    causal=False,
                    return_attn_probs=False,
                    deterministic=False,
                )
        return atten_out # type: ignore
    
    @override
    def forward(self, x: torch.Tensor, x_kv: Optional[torch.Tensor] = None, copy_first_head_kv: bool=False, attn_mask: torch.Tensor | None = None) -> torch.Tensor:
        """
        x: [batch_size, seq_len, feature, embed_dim]
        kv: Optional[batch_size, seq_len_kv, feature, embed_dim] — only needed if qkv_combined=False
        copy_first_head: Reuse the results from the first attention head
        """
        # feature attention: [B S F E]  
        # item attention: [B F S E]
        # B, T, C = x.shape
        B, S, F, D = x.shape
        assert x.shape[-1] == self.embed_dim
        
        x = x.view(-1, *x.shape[-2:])
        
        qkv = None
        q = None
        kv = None
        # batch_size = None
        # seqlen = None
        if self.qkv_combined:
            qkv = torch.einsum("... s, j h d s -> ... j h d", x, self.out_proj_weight)
        else:
            assert x_kv is not None, "kv combined attention requires kv input"
            q = torch.einsum("... s, h d s -> ... h d", x, self.q_proj_weight)
            if copy_first_head_kv:
                kv_weights = self.kv_proj_weight[:,:1]
                kv = torch.einsum("... s, j h d s -> ... j h d", x_kv, kv_weights)
                expand_shape = [-1 for _ in kv.shape]
                expand_shape[-2] = self.num_heads
                kv = kv.expand(*expand_shape)
            else:
                kv = torch.einsum("... s, j h d s -> ... j h d", x_kv, self.kv_proj_weight)
                
        if attn_mask is None and HAVE_FLASH_ATTN:
            atten_out = self.compute_attention_by_flashattn(qkv, q, kv)
        else:
            atten_out = self.compute_attention_by_torch(qkv, q, kv, attn_mask)
                
        atten_out = atten_out.reshape(B, S, self.num_heads, self.head_dim)

        out = torch.einsum(
            "... h d, h d s -> ... s",
            atten_out,
            self.out_proj_weight,
        )

        return out

class EncoderBaseLayer(nn.Module):
    "Base encoder layer of the Transformer model"
    def __init__(self, 
                 nhead: int, 
                 embed_dim: int, 
                 hid_dim:int, 
                 dropout: float=0,
                 pre_norm: bool=False,
                 activation: Literal['relu', 'gelu']='gelu',
                 layer_norm_eps: float=1e-5,
                 device: torch.device|None=None,
                 dtype: torch.dtype|None=None,
                 recompute_attn: bool=False,
                 qkv_w_init_method:Literal['xavier_uniform','kaiming_uniform']='xavier_uniform',
                 mlp_init_std:float=0,
                 mlp_use_residual:bool=False,
                 layer_arch: Literal[
                            "fmfmsm",
                            "smf",
                            "fsm",
                        ] = 'smf',
                 ):
        super().__init__()
        self.nhead = nhead
        self.embed_dim = embed_dim
        self.hid_dim = hid_dim
        self.dropout = dropout
        self.pre_norm = pre_norm
        self.activation = activation
        self.layer_norm_eps = layer_norm_eps
        self.device = device
        self.dtype = dtype
        self.layer_arch = layer_arch
        self.head_dim = self.embed_dim // self.nhead
        self.recompute_attn = recompute_attn
        self.mlp_init_std = mlp_init_std
        self.mlp_use_residual = mlp_use_residual
        self.qkv_w_init_method = qkv_w_init_method
        
        self.feature_attentions = []
        self.sequence_attentions = []
        self.mlp = []
        self.feature_attn_num = 1           # feature attention number
        self.items_attn_num = 1             # items attention number
        self.mlp_num = 1                    # mlp number
        
        if layer_arch == 'fmfmsm':
            self.feature_attn_num = 2
            self.mlp_num = 3
        
        # attention+MLP
        for _ in range(self.feature_attn_num):
            self.feature_attentions.append(MultiheadAttention(
                embed_dim=self.embed_dim,
                num_heads=self.nhead,
                device=self.device,
                dtype=self.dtype,
                qkv_combined=True,
                qkv_w_init_method=self.qkv_w_init_method,
                dropout=self.dropout,
                recompute=self.recompute_attn,
                mlp_init_std=self.mlp_init_std
            ))
        for _ in range(self.items_attn_num):
            self.sequence_attentions.append(MultiheadAttention(
                embed_dim=self.embed_dim,
                num_heads=self.nhead,
                device=self.device,
                dtype=self.dtype,
                qkv_combined=False,
                qkv_w_init_method=self.qkv_w_init_method,
                dropout=self.dropout,
                recompute=self.recompute_attn,
                mlp_init_std=self.mlp_init_std
            ))
        for _ in range(self.mlp_num):
            self.mlp.append(MLP(
                in_features=self.embed_dim,
                hidden_size=self.hid_dim,
                out_features=self.embed_dim,
                has_bias=False,
                device=self.device,
                dtype=self.dtype,
                activation=self.activation,
                depth=2,
                init_std=self.mlp_init_std
            ))
        
        self.layer_steps = []
        if self.layer_arch == 'fmfmsm':
            assert len(self.feature_attentions) >= 2 and len(self.sequence_attentions) >= 1 and len(self.mlp) >= 3
            self.layer_steps = [
                                partial(
                                    self.call_features_attention,
                                    index=0
                                ),
                                self.mlp[0],
                                partial(
                                    self.call_features_attention,
                                    index=1
                                ),
                                self.mlp[1],
                                partial(
                                    self.call_sequence_attention,
                                    index=0
                                ),
                                self.mlp[2]
            ]
        elif self.layer_arch == 'smf':
            assert len(self.feature_attentions) >= 1 and len(self.sequence_attentions) >= 1 and len(self.mlp) >= 1
            self.layer_steps = [
                                partial(
                                    self.call_sequence_attention,
                                    index=0
                                ),
                                self.mlp[0],
                                partial(
                                    self.call_features_attention,
                                    index=0
                                )
            ]
        elif self.layer_arch == 'fsm':
            assert len(self.feature_attentions) >= 1 and len(self.sequence_attentions) >= 1 and len(self.mlp) >= 1
            self.layer_steps = [
                                partial(
                                    self.call_features_attention,
                                    index=0
                                ),
                                partial(
                                    self.call_sequence_attention,
                                    index=0
                                ),
                                self.mlp[0]
            ]
        else:
            raise ValueError(f"Unsupport layr arch: {self.layer_arch}")
    
        self.layer_norms = nn.ModuleList(
            [
                LayerNormMixedPrecision(normalized_shape=self.embed_dim, eps=self.layer_norm_eps, 
                                        elementwise_affine=False, device=self.device, dtype=self.dtype)
                for _ in range(len(self.layer_steps))
            ]
        )
    
    def create_attn_mask(self, q_mask:torch.Tensor, k_mask:torch.Tensor)->torch.Tensor:
        """
        Create attention mask
        
        Args:
            q_mask (torch.Tensor): Query sequence mask, with shape [batch_size, head_count, q_seq_len]
            k_mask (torch.Tensor): Key sequence mask, with shape   [batch_size, head_count, k_seq_len]
        
        Returns:
            torch.Tensor: attention mask, with shape [batch_size, head_count, q_seq_len, k_seq_len]
        """
        _, _, q_seq_len = q_mask.shape
        _, _, k_seq_len = k_mask.shape
        
        q_mask_bool = q_mask.bool()  # [batch_size, head_count, q_seq_len]
        k_mask_bool = k_mask.bool()  # [batch_size, head_count, k_seq_len]
        
        q_expanded = q_mask_bool.unsqueeze(-1)
        k_expanded = k_mask_bool.unsqueeze(-2)
        
        valid_attn = q_expanded & k_expanded
        attn_mask = ~valid_attn
        _, _, q_seq_len, k_seq_len = attn_mask.shape
        attn_mask = attn_mask.reshape(-1, q_seq_len, k_seq_len)
        attn_mask = attn_mask.unsqueeze(1).expand(-1, 6, -1, -1)
        
        return attn_mask
    
    def call_features_attention(self, x:torch.Tensor, feature_atten_mask:torch.Tensor|None, eval_pos:int, index:int=0):
        assert len(self.feature_attentions) > index
        attn_mask = None
        if feature_atten_mask is not None:
            attn_mask = self.create_attn_mask(feature_atten_mask, feature_atten_mask)
        return self.feature_attentions[index](
                x,
                x_kv=None,
                attn_mask=attn_mask
            )
        
    def call_sequence_attention(self, x:torch.Tensor, feature_atten_mask:torch.Tensor|None, eval_pos:int, index:int=0):
        assert len(self.sequence_attentions) > index
        if eval_pos < x.shape[1]:
            x_test = self.sequence_attentions[index](
                        x = x[:, eval_pos:].transpose(1, 2),
                        x_kv = x[:, :eval_pos].transpose(1, 2),
                        copy_first_head_kv = True
                    ).transpose(1, 2)
        else:
            x_test = None
            print(f"Warning: eval_pos >= x.shape[1]!")
        x_train = self.self_attn_between_items(
                        x = x[:, :eval_pos].transpose(1, 2),
                        x_kv = x[:, :eval_pos].transpose(1, 2)
                    ).transpose(1, 2)
        
        if x_test is not None:
            return torch.cat([x_test, x_train], dim=1)
        else:
            return x_train
       
    
    def forward(self, x:torch.Tensor, feature_atten_mask:torch.Tensor, eval_pos:int) -> torch.Tensor:
        for sublayer, layer_norm in zip(self.layer_steps, self.layer_norms):
            if self.pre_norm:
                residual = x
                x = layer_norm(x)
                if isinstance(sublayer, functools.partial):
                    x = sublayer(x, feature_atten_mask, eval_pos)
                else:
                    x = sublayer(x)
                x = x + residual
            else:
                residual = x
                if isinstance(sublayer, functools.partial):
                    x = sublayer(x, feature_atten_mask, eval_pos)
                else:
                    x = sublayer(x)
                x = layer_norm(x)
                if isinstance(sublayer, functools.partial) or (isinstance(sublayer, MLP) and self.mlp_use_residual):
                    x = x + residual
        return x
                
