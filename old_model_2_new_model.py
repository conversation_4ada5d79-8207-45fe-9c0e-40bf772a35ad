import torch
import argparse
from model.transformer import FeaturesTransformer

parser = argparse.ArgumentParser(description='模型结构转换器')
parser.add_argument('-i', '--input', type=str, required=True, help="输入的模型名称")
parser.add_argument('-o', '--output', type=str, required=True, help="输出的模型名称")

args = parser.parse_args()

state_dict = torch.load(args.input)

print("fff")

new_state_dict = {}
new_state_dict['state_dict'] = state_dict['state_dict']
new_state_dict['config']={}

new_state_dict['config']['preprocess_config_x'] = {
    "num_features":state_dict['config_sample']['features_per_group'],
    "nan_handling_enabled": True,
    "normalize_on_train_only": True,
    "normalize_x": True,
    "remove_outliers": False,
    "normalize_by_used_features": True,
}
new_state_dict['config']['encoder_config_x'] = {
    "num_features":state_dict['config_sample']['features_per_group'],
    "embedding_size":state_dict['config_sample']['emsize'],
    "mask_embedding_size":state_dict['config_sample']['mask_embedding_size'],
    "encoder_use_bias": False,
    "feature_embedding_type":state_dict['config_sample']['feature_embedding_type'],
}
new_state_dict['config']['encoder_config_y'] = {
    "num_inputs":1,
    "embedding_size":state_dict['config_sample']['emsize'],
    "nan_handling_y_encoder":True,
    "max_num_classes":state_dict['config_sample']['max_num_classes'],
    "yemb_freeze_type":state_dict['config_sample']['yemb_type'],
}
new_state_dict['config']['decoder_config'] = {
    "num_classes": state_dict['config_sample']['max_num_classes']
}
new_state_dict['config']['feature_positional_embedding_type'] = state_dict['config_sample']['feature_positional_embedding']
new_state_dict['config']['classify_reg_mixed'] = state_dict['config_sample']['classify_reg_mixed']
new_state_dict['config']['nlayers'] = state_dict['config_sample']['nlayers']
new_state_dict['config']['nhead'] = state_dict['config_sample']['nhead']
new_state_dict['config']['embed_dim'] = state_dict['config_sample']['nhead']
new_state_dict['config']['hid_dim'] = state_dict['config_sample']['emsize']
new_state_dict['config']['mask_feature_embedding_type'] = state_dict['config_sample']['feature_embedding_type']
new_state_dict['config']['enable_mask_feature_pred'] = state_dict['config_sample']['enable_mask_feature_pred']
new_state_dict['config']['enable_mask_indicator_pred'] = state_dict['config_sample']['enable_mask_indicator_pred']
new_state_dict['config']['features_per_group'] = state_dict['config_sample']['features_per_group']
new_state_dict['config']['dropout'] = state_dict['config_sample']['dropout']
new_state_dict['config']['pre_norm'] = state_dict['config_sample']['use_pre_norm']
new_state_dict['config']['recompute_attn'] = state_dict['config_sample']['recompute_attn']
new_state_dict['config']['mlp_init_std'] = state_dict['config_sample']['init_std']
new_state_dict['config']['mlp_use_residual'] = True
new_state_dict['config']['layer_arch'] = state_dict['config_sample']['layer_arch']
if new_state_dict['config']['layer_arch'] == 'fmfmim':
    new_state_dict['config']['layer_arch'] = 'fmfmsm'
if new_state_dict['config']['layer_arch'] == 'imf':
    new_state_dict['config']['layer_arch'] = 'smf'
if new_state_dict['config']['layer_arch'] == 'fim':
    new_state_dict['config']['layer_arch'] = 'fsm'


config = new_state_dict['config']
model = FeaturesTransformer(
    preprocess_config_x = config['preprocess_config_x'],
    encoder_config_x = config['encoder_config_x'],
    encoder_config_y = config['encoder_config_y'],
    decoder_config = config['decoder_config'],
    feature_positional_embedding_type = config['feature_positional_embedding_type'],
    classify_reg_mixed = config['classify_reg_mixed'],
    nlayers = config['nlayers'],
    nhead = config['nhead'],
    embed_dim = config['embed_dim'],
    hid_dim = config['hid_dim'],
    mask_feature_embedding_type = config['mask_feature_embedding_type'],
    enable_mask_feature_pred = config['enable_mask_feature_pred'],
    enable_mask_indicator_pred = config['enable_mask_indicator_pred'],
    features_per_group = config['features_per_group'],
    dropout = config['dropout'],
    pre_norm = config['pre_norm'],
    recompute_attn = config['recompute_attn'],
    mlp_init_std = config['mlp_init_std'],
    mlp_use_residual = config['mlp_use_residual'],
    layer_arch = config['layer_arch']
)

model.load_state_dict(new_state_dict['state_dict'])

print("== 2")