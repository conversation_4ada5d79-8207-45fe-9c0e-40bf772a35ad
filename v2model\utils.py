
import torch
from torch import nn

from v2model.encoders import (
    InputNormalizationEncoderStep,
    LinearInputEncoderStep,
    MulticlassClassificationTargetEncoder,
    NanHandlingEncoderStep,
    RemoveDuplicateFeaturesEncoderStep,
    RemoveEmptyFeaturesEncoderStep,
    SequentialEncoder,
    VariableNumFeaturesEncoderStep,
    YInputEncoderStep,
)


def get_model_config(user_config:dict):
    config = {
        'emsize': user_config['emsize'], 
        'nhead': user_config['nhead'], 
        'nhid_factor': user_config['nhid_factor'], 
        'nlayers': user_config['nlayers'], 
        'feature_positional_embedding': user_config.get('feature_positional_embedding', 'subspace'), 
        'pre_norm': user_config.get('use_pre_norm', False), 
        'use_step_encoder': user_config.get('use_step_encoder', False), 
        'layer_arch': user_config.get('layer_arch',"default"),
        'features_per_group': user_config.get('features_per_group',2),
        'max_num_classes': user_config.get('max_num_classes',10),
        
        # 默认值
        'adaptive_max_seq_len_to_max_full_table_size': 150000, 
        'batch_size': 4, 
        'remove_duplicate_features': False, 
        'seq_len': 4000, 
        'task_type': 'multiclass', 
        'num_buckets': 1000, 
        'max_num_features': 85, 
        'two_sets_of_queries': None, 
        'aggregate_k_gradients': 1, 
        'differentiable_hps_as_style': False, 
        'dropout': 0.0, 
        'encoder_use_bias': False, 
        'multiquery_item_attention': False, 
        'nan_handling_enabled': True, 
        'nan_handling_y_encoder': True, 
        'normalize_by_used_features': True, 
        'normalize_on_train_only': True, 
        'normalize_to_ranking': False, 
        'normalize_x': True, 
        'num_global_att_tokens': 0, 
        'progress_bar': False, 
        'recompute_attn': False, 
        'recompute_layer': True, 
        'remove_empty_features': True, 
        'remove_outliers': False, 
        'semisupervised_enabled': False, 
        'timing': False, 
        'use_separate_decoder': False, 
        'use_flash_attention': True, 
        'multi_query_factor': None, 
        'multiquery_item_attention_for_test_set': True, 
        'attention_init_gain': None, 
    }
    return config


def get_encoder(  # noqa: PLR0913
    *,
    num_features: int,
    embedding_size: int,
    remove_empty_features: bool,
    remove_duplicate_features: bool,
    nan_handling_enabled: bool,
    normalize_on_train_only: bool,
    normalize_to_ranking: bool,
    normalize_x: bool,
    remove_outliers: bool,
    normalize_by_used_features: bool,
    encoder_use_bias: bool,
) -> nn.Module:
    inputs_to_merge = {"main": {"dim": num_features}}

    encoder_steps = []
    if remove_empty_features:
        # 移除无效（常量）数据
        encoder_steps += [RemoveEmptyFeaturesEncoderStep()]         

    if remove_duplicate_features:
        # 移除重复特征（未实现）
        encoder_steps += [RemoveDuplicateFeaturesEncoderStep()]

    # NaN处理编码器
    encoder_steps += [NanHandlingEncoderStep(keep_nans=nan_handling_enabled)]

    if nan_handling_enabled:
        inputs_to_merge["nan_indicators"] = {"dim": num_features}

        encoder_steps += [
            # 通过添加零值将输入转换为固定数量的特征，不归一化处理（方差不恒定）
            VariableNumFeaturesEncoderStep(
                num_features=num_features,
                normalize_by_used_features=False,
                in_keys=["nan_indicators"],
                out_keys=["nan_indicators"],
            ),
        ]

    encoder_steps += [
        # 数据归一化
        InputNormalizationEncoderStep(
            normalize_on_train_only=normalize_on_train_only,
            normalize_to_ranking=normalize_to_ranking,
            normalize_x=normalize_x,
            remove_outliers=remove_outliers,
        ),
    ]

    encoder_steps += [
        # 通过添加零值将输入转换为固定数量的特征，归一化处理（方差恒定）
        VariableNumFeaturesEncoderStep(
            num_features=num_features,
            normalize_by_used_features=normalize_by_used_features,
        ),
    ]

    encoder_steps += [
        # 线性编码器，不使用0替换NaN值
        LinearInputEncoderStep(
            num_features=sum([i["dim"] for i in inputs_to_merge.values()]),
            emsize=embedding_size,
            bias=encoder_use_bias,
            in_keys=tuple(inputs_to_merge),
            out_keys=("output",),
        ),
    ]

    return SequentialEncoder(*encoder_steps, output_key="output")


def get_y_encoder(
    *,
    num_inputs: int,
    embedding_size: int,
    nan_handling_y_encoder: bool,
    max_num_classes: int,
    use_step_encoder: bool = False,
) -> nn.Module:
    steps = []
    inputs_to_merge = [{"name": "main", "dim": num_inputs}]
    if nan_handling_y_encoder:
        steps += [NanHandlingEncoderStep()]
        inputs_to_merge += [{"name": "nan_indicators", "dim": num_inputs}]

    if max_num_classes >= 2:
        # 多标签类别编码
        steps += [MulticlassClassificationTargetEncoder()]

    if use_step_encoder:
        steps += [
            YInputEncoderStep(
                emsize=embedding_size,
                n_classes=max_num_classes,
                in_keys=("main",),
                out_keys=("output",),
            )
        ]
    else:
        steps += [
            LinearInputEncoderStep(
                num_features=sum([i["dim"] for i in inputs_to_merge]),  # type: ignore
                emsize=embedding_size,
                in_keys=tuple(i["name"] for i in inputs_to_merge),  # type: ignore
                out_keys=("output",),
            ),
        ]
    
    return SequentialEncoder(*steps, output_key="output")