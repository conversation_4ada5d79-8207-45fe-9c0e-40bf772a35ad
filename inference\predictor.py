from inference.preprocess import FeatureShuffler, FilterValidFeatures, CategoricalFeatureEncoder, RebalanceFeatureDistribution
from model.transformer import FeaturesTransformer
import torch
from typing import List, Tuple, Literal
import random
from sklearn.utils.validation import check_X_y, check_array
from sklearn.preprocessing import <PERSON><PERSON>nco<PERSON>, OrdinalEncoder
from sklearn.compose import ColumnTransformer, make_column_selector
from sklearn.preprocessing import FunctionTransformer
import numpy as np
from itertools import chain, product, repeat
import pandas as pd
import einops


NA_PLACEHOLDER = "__MISSING__"

class LDMPredictor:
    def __init__(self, 
                 device:torch.device, 
                 model_path:str, 
                 mix_precision:bool=True, 
                 categorical_features_indices:List[int]|None=None,
                 n_estimators: int=4,
                 outlier_remove_std: float=12,
                 softmax_temperature:float=0.9,
                 task_type: Literal['classification', 'Regression']='classification',
                 mask_prediction:bool=False,
                 seed:int=0,
                 new_model:bool=False):
        self.model_path = model_path
        self.device = device
        self.mix_precision = mix_precision
        self.categorical_features_indices = categorical_features_indices
        self.seed = seed
        self.n_estimators = n_estimators
        self.model = None
        self.outlier_remove_std = outlier_remove_std
        self.class_shuffle_factor = 3
        self.min_seq_len_for_category_infer = 100
        self.max_unique_num_for_category_infer = 30
        self.min_unique_num_for_numerical_infer = 4
        self.preprocess_num = 4
        self.softmax_temperature = softmax_temperature
        self.task_type = task_type
        self.model_type = ""
        self.new_model = new_model
        self.mask_prediction = mask_prediction
        
        
        self.load_model()
        
        self.preprocess_pipelines = []
        self.preprocess_configs = []
        
        random.seed(seed)
        rand_gen = np.random.default_rng(seed)
        self.seeds = [random.randint(0, 10000) for _ in range(n_estimators*self.preprocess_num)]
        start_idx = rand_gen.integers(0, 1000)
        all_shifts = list(range(start_idx, start_idx + n_estimators))  # 转换为list
        self.all_shifts = rand_gen.choice(all_shifts, size=n_estimators, replace=False)
        
        if self.task_type == "classification":
            self._build_cls_pipeline()
        elif self.task_type == "Regression":
            self._build_reg_pipeline()
        else:
            raise ValueError(f"Unsupported task type, supported tasks include classification and regression!")

        if self.model_type != "mask_model" and self.task_type == "Regression":
            raise ValueError(f"When in regression mode, the model type is required to be mask_model")
            
    def _build_cls_pipeline(self):
        config_dict = [
            {
                "RebalanceFeatureDistribution":{
                    "worker_tag": "quantile_uniform_10",
                    "discrete_flag": False,
                    "original_flag": True,
                    "svd_tag": 'svd'
                },
                "CategoricalFeatureEncoder":{
                    "encoding_strategy":"ordinal_strict_feature_shuffled"
                },
                "FeatureShuffler":{
                    "mode": "shuffle",
                }
            },
            {
                "RebalanceFeatureDistribution":{
                    "worker_tag": None,
                    "discrete_flag": True,
                    "original_flag": False,
                    "svd_tag": None
                },
                "CategoricalFeatureEncoder":{
                    "encoding_strategy":"numeric"
                },
                "FeatureShuffler":{
                    "mode": "shuffle",
                }
            }
        ]
        for i in range(self.n_estimators):
            idx = 0 if i < self.n_estimators // 2 else 1
            pipeline = []
            pipeline.append(FilterValidFeatures())
            pipeline.append(RebalanceFeatureDistribution(**config_dict[idx]['RebalanceFeatureDistribution']))
            pipeline.append(CategoricalFeatureEncoder(**config_dict[idx]['CategoricalFeatureEncoder']))
            pipeline.append(FeatureShuffler(**config_dict[idx]['FeatureShuffler'], offset=self.all_shifts[i]))
        
            self.preprocess_pipelines.append(pipeline)

    def _build_reg_pipeline(self):
        config_dict = [
            {
                "RebalanceFeatureDistribution":{
                    "worker_tag": "quantile_uniform_all_data",
                    "discrete_flag": False,
                    "original_flag": True,
                    "svd_tag": 'svd'
                },
                "CategoricalFeatureEncoder":{
                    "encoding_strategy":"ordinal_strict_feature_shuffled"
                },
                "FeatureShuffler":{
                    "mode": "shuffle",
                }
            },
            {
                "RebalanceFeatureDistribution":{
                    "worker_tag": None,
                    "discrete_flag": True,
                    "original_flag": False,
                    "svd_tag": None
                },
                "CategoricalFeatureEncoder":{
                    "encoding_strategy":"onehot"
                },
                "FeatureShuffler":{
                    "mode": "shuffle",
                }
            }
        ]
        for i in range(self.n_estimators):
            idx = 0 if i < self.n_estimators // 2 else 1
            pipeline = []
            pipeline.append(FilterValidFeatures())
            pipeline.append(RebalanceFeatureDistribution(**config_dict[idx]['RebalanceFeatureDistribution']))
            pipeline.append(CategoricalFeatureEncoder(**config_dict[idx]['CategoricalFeatureEncoder']))
            pipeline.append(FeatureShuffler(**config_dict[idx]['FeatureShuffler'], offset=self.all_shifts[i]))
        
            self.preprocess_pipelines.append(pipeline)

    def load_model(self):
        state_dict = torch.load(self.model_path, map_location=self.device)
        if 0 in state_dict and 2 in state_dict:
            self._load_model_v2(state_dict=state_dict)
        else:
            if self.new_model:
                self._load_model(state_dict=state_dict)
            else:
                self._load_model_mask(model_path=self.model_path)
    
    def _modify_outliers_param(self):
        if hasattr(self.model, "encoder"):
            encoder=self.model.encoder
            norm_encoder = None
            for ec in encoder:
                if "InputNormalizationEncoderStep" in str(ec.__class__):
                    norm_encoder = ec
                    if norm_encoder:
                        if self.task_type == "Regression":
                            norm_encoder.remove_outliers = False
                        else:
                            norm_encoder.remove_outliers = self.outlier_remove_std > 0
                            norm_encoder.remove_outliers_sigma = self.outlier_remove_std
                elif 'NormalizationEncoder' in str(ec.__class__):
                    norm_encoder = ec
                    if norm_encoder:
                        if self.task_type == "Regression":
                            norm_encoder.remove_outliers = False
                        else:
                            norm_encoder.remove_outliers = self.outlier_remove_std > 0
                            norm_encoder.std_sigma = self.outlier_remove_std
    
    def _load_model(self, state_dict:dict):
        self.model_type = 'mask_model'
        
        config = state_dict['config']
        self.model = FeaturesTransformer(
            preprocess_config_x = config['preprocess_config_x'],
            encoder_config_x = config['encoder_config_x'],
            encoder_config_y = config['encoder_config_y'],
            decoder_config = config['decoder_config'],
            feature_positional_embedding_type = config['feature_positional_embedding_type'],
            classify_reg_mixed = config['classify_reg_mixed'],
            nlayers = config['nlayers'],
            nhead = config['nhead'],
            embed_dim = config['embed_dim'],
            hid_dim = config['hid_dim'],
            mask_feature_embedding_type = config['mask_feature_embedding_type'],
            enable_mask_feature_pred = config['enable_mask_feature_pred'],
            enable_mask_indicator_pred = config['enable_mask_indicator_pred'],
            features_per_group = config['features_per_group'],
            dropout = config['dropout'],
            pre_norm = config['pre_norm'],
            activation = config['activation'],
            layer_norm_eps = config['layer_norm_eps'],
            device = torch.device(config['device']),
            dtype = config['dtype'],
            recompute_attn = config['recompute_attn'],
            qkv_w_init_method = config['qkv_w_init_method'],
            mlp_init_std = config['mlp_init_std'],
            mlp_use_residual = config['mlp_use_residual'],
            layer_arch = config['layer_arch']
        )
        self.model.load_state_dict(state_dict['state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        # 修改outlier的参数
        self._modify_outliers_param()
    
    def _load_model_mask(self, model_path:str):
        from maskmodel import loading
        self.model_type = 'mask_model'
        self.model, _, _ = loading.load_model(path=model_path, model_seed=0, model_config={'enable_mask_feature_pred': self.mask_prediction})
        self._modify_outliers_param()
        self.model.to(self.device)
        self.model.eval()
       

    def _load_model_v2(self, state_dict:dict):
        from v2model.transformer import PerFeatureTransformer as v2_PerFeatureTransformer
        from v2model.utils import get_model_config, get_encoder, get_y_encoder
        
        assert 0 in state_dict and 2 in state_dict, f"Unsupport state_dict type! Keys 0, 1, 2, 3 must be present in the dictionary!"
        
        config = get_model_config(state_dict[2])
        # config = state_dict['config']     # TODO 修正模型保存的结构
        self.model = v2_PerFeatureTransformer(
            seed=0,
            encoder=get_encoder(
                num_features=config['features_per_group'],
                embedding_size=config['emsize'],
                remove_empty_features=config['remove_empty_features'],
                remove_duplicate_features=config['remove_duplicate_features'],
                nan_handling_enabled=config['nan_handling_enabled'],
                normalize_on_train_only=config['normalize_on_train_only'],
                normalize_to_ranking=config['normalize_to_ranking'],
                normalize_x=config['normalize_x'],
                remove_outliers=config['remove_outliers'],
                normalize_by_used_features=config['normalize_by_used_features'],
                encoder_use_bias=config['encoder_use_bias'],
            ),
            y_encoder=get_y_encoder(
                num_inputs=1,
                embedding_size=config['emsize'],
                nan_handling_y_encoder=config['nan_handling_y_encoder'],
                max_num_classes=config['max_num_classes'],
                use_step_encoder=config.get("use_step_encoder", False)
            ),
            nhead=config['nhead'],
            ninp=config['emsize'],
            nhid=config['emsize'] * config['nhid_factor'],
            nlayers=config['nlayers'],
            features_per_group=config['features_per_group'],
            cache_trainset_representation=False,
            init_method=None,
            decoder_dict={"standard": (None, config['max_num_classes'])},
            use_encoder_compression_layer=False,
            recompute_attn= config['recompute_attn'],
            recompute_layer=config['recompute_layer'],
            feature_positional_embedding=config['feature_positional_embedding'],
            use_separate_decoder=config['use_separate_decoder'],
            layer_norm_with_elementwise_affine=False,
            nlayers_decoder=None,
            pre_norm=config.get('pre_norm', False),
            layer_arch=config.get('layer_arch', 'default'),
            multiquery_item_attention=config['multiquery_item_attention'],
            multiquery_item_attention_for_test_set=config['multiquery_item_attention_for_test_set'],
            attention_init_gain=(
                config['attention_init_gain']
                if config['attention_init_gain'] is not None
                else 1.0
            ),
            two_sets_of_queries=(
                config['two_sets_of_queries']
                if config['two_sets_of_queries'] is not None
                else False
            ),
            dropout_p=config['dropout'],
        )
        self.model.load_state_dict(state_dict[0])
        self.model.to(self.device)
        self.model.eval()
        
        # 修改outlier的参数
        self._modify_outliers_param()
    
    def _check_n_features(self, X, reset):
        """检查特征数是否与之前一致"""
        n_features = X.shape[1]
        if reset:
            self.n_features_in_ = n_features
        else:
            if self.n_features_in_ != n_features:
                raise ValueError(
                    f"X has {n_features} features, "
                    f"but this estimator is expecting {self.n_features_in_} features."
                )
    
    def validate_data(self, x=None, y=None, reset=True, validate_separately=False, **check_params):
        """
        {'accept_sparse': False, 'dtype': None, 'ensure_all_finite': 'allow-nan'}
        """
        # 同时检查 X 和 y
        if y is not None:
            x, y = check_X_y(x, y, **check_params)
            self._check_n_features(x, reset=reset)
            return x, y

        # 只检查 X
        if x is not None:
            x = check_array(x, **check_params)
            self._check_n_features(x, reset=reset)
            return x


        # 如果两者都是 "no_validation"，直接返回 None
        return None
    
    def convert_x_dtypes(self, x:np.ndarray, dtypes:Literal["float32", "float64"] = "float64"):
        NUMERIC_DTYPE_KINDS = "?bBiufm"
        OBJECT_DTYPE_KINDS = "OV"
        STRING_DTYPE_KINDS = "SaU"
        
        if x.dtype.kind in NUMERIC_DTYPE_KINDS:
            x = pd.DataFrame(x, copy=False, dtype=dtypes)
        elif x.dtype.kind in OBJECT_DTYPE_KINDS:
            x = pd.DataFrame(x, copy=True)
            x = x.convert_dtypes()
        else:
            raise ValueError(f"Unsupport string dtypes! {x.dtype}")

        integer_columns = x.select_dtypes(include=["number"]).columns
        if len(integer_columns) > 0:
            x[integer_columns] = x[integer_columns].astype(dtypes)
        return x
    
    def convert_category2num(self, x, dtype:np.floating=np.float64, placeholder: str = NA_PLACEHOLDER,):
        ordinal_encoder = OrdinalEncoder(categories="auto",
                                        dtype=dtype,
                                        handle_unknown="use_encoded_value",
                                        unknown_value=-1,
                                        encoded_missing_value=np.nan)
        col_encoder = ColumnTransformer(transformers=[("encoder", ordinal_encoder, make_column_selector(dtype_include=["category", "string"]))],
                                        remainder=FunctionTransformer(),
                                        sparse_threshold=0.0,
                                        verbose_feature_names_out=False,
                                    )
        
        string_cols = x.select_dtypes(include=["string", "object"]).columns
        if len(string_cols) > 0:
            x[string_cols] = x[string_cols].fillna(placeholder)
        
        X_encoded = col_encoder.fit_transform(x)

        string_cols_ix = [x.columns.get_loc(col) for col in string_cols]
        placeholder_mask = x[string_cols] == placeholder
        string_cols_ix_2 = list(range(len(string_cols_ix)))
        X_encoded[:, string_cols_ix_2] = np.where(
            placeholder_mask,
            np.nan,
            X_encoded[:, string_cols_ix_2],
        )

        return X_encoded

    
    def get_categorical_features_indices(self, x:np.ndarray):
        if x.shape[0] < self.min_seq_len_for_category_infer:
            return []
        categorical_idx = []
        n_categories = []
        for idx, col in enumerate(x.T):
            unique_arr = np.unique(col)
            unique_arr = unique_arr[~np.isnan(unique_arr)]
            if len(unique_arr) < self.min_unique_num_for_numerical_infer:
                categorical_idx.append(idx)
                n_categories.append(len(unique_arr))
        return categorical_idx, n_categories
        
    
    def predict(self, x_train:np.ndarray, y_train:np.ndarray, x_test:np.ndarray) -> np.ndarray:
        if self.task_type == "classification":
            return self._predict_cls(x_train, y_train, x_test)
        elif self.task_type == "Regression":
            return self._predict_reg(x_train, y_train, x_test) # actually pandas dataframe
        else:
            raise ValueError(f"Unsupported task type, supported tasks include classification and regression!")
        
    def _predict_cls(self, x_train:np.ndarray, y_train:np.ndarray, x_test:np.ndarray) -> np.ndarray:
        np_rng = np.random.default_rng(self.seed)
        
        # 确保x_train和x_test都被正确处理
        x_train, y_train = self.validate_data(x_train, y_train, reset=True, validate_separately=False, accept_sparse=False, dtype=None, force_all_finite=False)
        x_test = self.validate_data(x_test, reset=True, validate_separately=False, accept_sparse=False, dtype=None, force_all_finite=False)
        
        # 拼接x_train和x_test
        x = np.concatenate([x_train, x_test], axis=0)
        
        # 对y_train进行编码
        self.label_encoder = LabelEncoder()
        y = self.label_encoder.fit_transform(y_train)
        self.classes = self.label_encoder.classes_
        self.n_classes = len(self.classes)
        
        # 对y进行shuffle
        noise = np_rng.random((self.n_estimators * self.class_shuffle_factor, self.n_classes))
        shufflings = np.argsort(noise, axis=1)
        uniqs = np.unique(shufflings, axis=0)
        balance_count = self.n_estimators // len(uniqs)
        self.class_permutations = list(chain.from_iterable(repeat(elem, balance_count) for elem in uniqs))
        cout = self.n_estimators%len(uniqs)
        if self.n_estimators%len(uniqs) > 0:
            self.class_permutations += [uniqs[i] for i in np_rng.choice(len(uniqs), size=cout)]
        
        # y = self.class_permutations[y.copy()]
        
        # 对x进行预处理
        x = self.convert_x_dtypes(x)
        x = self.convert_category2num(x)
        categorical_idx, n_categories = self.get_categorical_features_indices(x)
        outputs = []
        mask_predictions = []
        for id_pipe, pipe in enumerate(self.preprocess_pipelines):
            x_ = x.copy()
            y_ = self.class_permutations[id_pipe][y.copy()]
            categorical_idx_ = categorical_idx.copy()
            for id_step, step in enumerate(pipe):
                if isinstance(step, RebalanceFeatureDistribution):
                    x_train_ = x_[:len(y_train)]
                    x_test_ = x_[len(y_train):]
                    if x_train_.shape[1] != x_test_.shape[1]:
                        x_test_ = x_test_[:, :x_train_.shape[1]]
                    x_train_, categorical_idx_ = step.fit_transform(x_train_, categorical_idx_, self.seeds[id_pipe*self.preprocess_num+id_step])
                    x_test_, categorical_idx_ = step.transform(x_test_)
                    x_ = np.concatenate([x_train_, x_test_], axis=0)
                else:
                    x_, categorical_idx_ = step.fit_transform(x_, categorical_idx_, self.seeds[id_pipe*self.preprocess_num+id_step])
                    # print(f"step {id_step} categorical_idx_ {categorical_idx_}")
            
            x_ = torch.from_numpy(x_[:,np.newaxis,:]).float().to(self.device)
            y_ = torch.from_numpy(y_).float().to(self.device)
            with(torch.autocast(device_type='cuda', enabled=self.mix_precision), torch.inference_mode()):
                if self.model_type == "mask_model" and self.mask_prediction:
                    output=self.model((None, x_, y_), only_return_standard_out=True, categorical_inds=categorical_idx_, single_eval_pos=len(y_), y_type=0)
                    process_config = output['process_config']
                    output_feature_pred = self.PostProcessInModel(output['feature_pred'], process_config)
                    output_feature_pred = self.PostProcess(output_feature_pred, pipe, process_config)
                    mask_predictions.append(output_feature_pred)
                    output = output['cls_output']
                elif self.model_type == "mask_model":
                    output=self.model((None, x_, y_), only_return_standard_out=True, categorical_inds=categorical_idx_, single_eval_pos=len(y_), y_type=0)
                else:
                    output=self.model((None, x_, y_), only_return_standard_out=True, categorical_inds=categorical_idx_, single_eval_pos=len(y_))
                output = output if isinstance(output, dict) else output.squeeze(1)
                if self.softmax_temperature != 1:
                    output = (output[:, :self.n_classes].float() / self.softmax_temperature)

                output = output[..., self.class_permutations[id_pipe]]
            outputs.append(output)
            
        outputs = [torch.nn.functional.softmax(o, dim=1) for o in outputs]
        output = torch.stack(outputs).mean(dim=0)
        mask_prediction = np.stack(mask_predictions).mean(axis=0) if mask_predictions != [] else None
        output = output.float().cpu().numpy()
        return output / output.sum(axis=1, keepdims=True), mask_prediction
    
    def PostProcessInModel(self, feature_pred:torch.tensor, config: dict) -> torch.tensor:
        # Revert preprocess in model forward
        feature_pred = feature_pred / torch.sqrt(config['features_per_group'] / config['num_used_features'].to(self.device))
        feature_pred = feature_pred*config['std_for_normalization'] + config['mean_for_normalization']
        feature_pred = einops.rearrange(feature_pred, "b s f n -> s b (f n)").squeeze(1).float().cpu().numpy()
        if config['n_x_padding'] > 0:
            feature_pred = feature_pred[:,:-config['n_x_padding']]
        return feature_pred
    
    def PostProcess(self, feature_pred:np.ndarray, pipeline:List, config: dict, gt=False) -> np.ndarray:        
        # Revert preprocess in the Classifier
        for id_step, step in enumerate(reversed(pipeline)):
            if isinstance(step, FeatureShuffler):
                if step.mode == "shuffle":
                    inv_p = np.argsort(step.feature_indices)
                    feature_pred = feature_pred[:, inv_p]
                else:
                    raise NotImplementedError
            elif isinstance(step, CategoricalFeatureEncoder):
                if step.encoding_strategy != 'onehot':
                    if step.category_mappings is not None:
                        categorical_indices = list(step.category_mappings.keys())
                        feature_pred[:, categorical_indices] = np.round(feature_pred[:, categorical_indices])
                    if step.transformer is not None:
                        for idx, p in step.category_mappings.items():
                            feature_pred[:, idx] = np.clip(feature_pred[:, idx], a_min=0, a_max=max(p))
                            inv_p = np.argsort(p)
                            feature_pred[:, idx] = inv_p[feature_pred[:, idx].astype(int)].astype(feature_pred.dtype)
                        inv_col = np.argsort(step.feature_indices)
                        feature_pred = feature_pred[:, inv_col]
                else:
                    if len(step.categorical_features) == 0 or step.transformer is None:
                        continue
                    cont_features_indices = [idx for idx in range(feature_pred.shape[1]) if idx not in step.categorical_features]
                    # TODO: 如果传入RebalanceFeatureDistribution的分类特征和连续特征是交错的，则还需要额外调整feature顺序。暂时没有实现。
                    assert np.array_equal(step.categorical_features, np.arange(len(step.categorical_features)))
                    start_idx = 0
                    for idx, out_category in enumerate(step.transformer.named_transformers_['one_hot_encoder'].categories_):
                        assert np.isnan(out_category).sum()==1
                        assert len(out_category) >= 2
                        if not np.any(np.isnan(out_category)):
                            if len(out_category) == 2: # e.g. [3, 5.5]
                                feature_pred[:,start_idx] = np.round(np.clip(feature_pred[:,start_idx], a_min=0, a_max=1))
                                start_idx += 1
                            else:
                                arr = feature_pred[:, start_idx:start_idx+len(out_category)]
                                feature_pred[:, start_idx:start_idx+len(out_category)] = (arr == arr.max(axis=1, keepdims=True)).astype(float)
                                start_idx += len(out_category)
                        else:
                            if len(out_category) == 2: # e.g. [0, nan]
                                feature_pred[:,start_idx] = 0
                                start_idx += 1
                            else:
                                arr = feature_pred[:, start_idx:start_idx+len(out_category)-1]
                                feature_pred[:, start_idx:start_idx+len(out_category)-1] = (arr == arr.max(axis=1, keepdims=True)).astype(float)
                                feature_pred[:, start_idx+len(out_category)-1] = 0
                                start_idx += len(out_category)
                    feature_pred = np.column_stack([step.transformer.named_transformers_['one_hot_encoder'].inverse_transform(feature_pred[:, step.categorical_features]), feature_pred[:, cont_features_indices]])
                    
            elif isinstance(step, RebalanceFeatureDistribution):
                if step.svd_tag == 'svd' and step.svd_n_comp > 0:
                    feature_pred = feature_pred[:, :-step.svd_n_comp]
                if step.worker_tag in ["quantile_uniform_10", "quantile_uniform_5", "quantile_uniform_all_data"] and step.n_quantile_features > 0:
                    feature_pred = feature_pred[:, :-step.n_quantile_features]
                elif step.worker_tag == "power":
                    # reverse power transform
                    # TODO: there could be NAN...
                    cont_features_indices = [idx for idx in range(feature_pred.shape[1]) if idx not in step.dis_ix]
                    feature_pred[:, cont_features_indices] = step.worker.named_transformers_['feat_transform'].inverse_transform(feature_pred[:, cont_features_indices])
                    # reverse feature order
                    inv_p = np.argsort(step.feature_indices)
                    feature_pred = feature_pred[:, inv_p]

                    
            elif isinstance(step, FilterValidFeatures):
                deleted_indices = np.where(step.invalid_indices)[0]
                if deleted_indices != []:
                    original_cols = len(deleted_indices) + feature_pred.shape[1]
                    restored = np.zeros((feature_pred.shape[0], original_cols))                
                    all_indices = set(range(original_cols))
                    kept_indices = list(all_indices - set(deleted_indices)) 
                    for i, idx in enumerate(kept_indices):
                        restored[:, idx] = feature_pred[:, i]                
                    for i, idx in enumerate(deleted_indices):
                        restored[:, idx] = step.invalid_features[:, i]
                    feature_pred = restored.copy()
        return feature_pred
        
    def _predict_reg(self, x_train:np.ndarray, y_train:np.ndarray, x_test:np.ndarray) -> np.ndarray:
        np_rng = np.random.default_rng(self.seed)
        
        # 确保x_train和x_test都被正确处理
        x_train, y_train = self.validate_data(x_train, y_train, reset=True, validate_separately=False, accept_sparse=False, dtype=None, force_all_finite=False)
        x_test = self.validate_data(x_test, reset=True, validate_separately=False, accept_sparse=False, dtype=None, force_all_finite=False)
        
        # 拼接x_train和x_test
        x = np.concatenate([x_train, x_test], axis=0)
    
        # 对x进行预处理
        x = self.convert_x_dtypes(x)
        x = self.convert_category2num(x)
        x = x.astype(float)
        categorical_idx, n_categories = self.get_categorical_features_indices(x)
    
        outputs = []
        mask_predictions = []
        for id_pipe, pipe in enumerate(self.preprocess_pipelines):
            x_ = x.copy()
            y_ = y_train.copy()
            categorical_idx_ = categorical_idx.copy()
            for id_step, step in enumerate(pipe):
                if isinstance(step, RebalanceFeatureDistribution):
                    x_train_ = x_[:len(y_train)]
                    x_test_ = x_[len(y_train):]
                    if x_train_.shape[1] != x_test_.shape[1]:
                        x_test_ = x_test_[:, :x_train_.shape[1]]
                    x_train_, categorical_idx_ = step.fit_transform(x_train_, categorical_idx_, self.seeds[id_pipe*self.preprocess_num+id_step])
                    x_test_, categorical_idx_ = step.transform(x_test_)
                    x_ = np.concatenate([x_train_, x_test_], axis=0)
                else:
                    x_, categorical_idx_ = step.fit_transform(x_, categorical_idx_, self.seeds[id_pipe*self.preprocess_num+id_step])
                    # print(f"step {id_step} categorical_idx_ {categorical_idx_}")
            
            x_ = torch.from_numpy(x_[:,np.newaxis,:]).float().to(self.device)
            y_ = torch.from_numpy(y_).float().to(self.device)
            with(torch.autocast(device_type='cuda', enabled=self.mix_precision), torch.inference_mode()):
                if self.model_type == "mask_model" and self.mask_prediction:
                    output=self.model((None, x_, y_), only_return_standard_out=True, categorical_inds=categorical_idx_, single_eval_pos=len(y_), y_type=1)
                    process_config = output['process_config']
                    output_feature_pred = self.PostProcessInModel(output['feature_pred'], process_config)
                    output_feature_pred = self.PostProcess(output_feature_pred, pipe, process_config)
                    mask_predictions.append(output_feature_pred)
                    output = output['reg_output']                 
                else:
                    output=self.model((None, x_, y_), only_return_standard_out=True, categorical_inds=categorical_idx_, single_eval_pos=len(y_), y_type=1)
                    
                output = output if isinstance(output, dict) else output.squeeze(1)

            outputs.append(output)
            
        output = torch.stack(outputs).squeeze(2).mean(dim=0)
        mask_prediction = np.stack(mask_predictions).mean(axis=0) if mask_predictions != [] else None
        
        return output, mask_prediction
