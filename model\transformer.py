import torch
import torch.nn as nn
from model.layer import Encoder<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from typing import Any, Literal
from model.encoders import get_x_encoder, get_cls_y_encoder, get_reg_y_encoder, preprocesss_4_x




class FeaturesTransformer(nn.Module):
    def __init__(
                self,
                *,
                preprocess_config_x:dict[str, Any],
                encoder_config_x:dict[str, Any],
                encoder_config_y:dict[str, Any],
                decoder_config:dict[str, Any],
                feature_positional_embedding_type:Literal['subspace','orthogonal','subortho'],
                classify_reg_mixed:bool,
                nlayers:int,
                nhead: int, 
                embed_dim: int, 
                hid_dim:int, 
                mask_feature_embedding_type:Literal['line_layer', 'neural_network', 'mask_embedding', 'add_mask_flag']='mask_embedding',
                enable_mask_feature_pred: bool = False,
                enable_mask_indicator_pred: bool = False,
                features_per_group:int = 2,
                dropout: float=0,
                pre_norm: bool=False,
                activation: Literal['relu', 'gelu']='gelu',
                layer_norm_eps: float=1e-5,
                device: torch.device|None=None,
                dtype: torch.dtype|None=None,
                recompute_attn: bool=False,
                qkv_w_init_method:Literal['xavier_uniform','kaiming_uniform']='xavier_uniform',
                mlp_init_std:float=0,
                mlp_use_residual:bool=False,
                layer_arch: Literal[
                        "fmfmsm",
                        "smf",
                        "fsm",
                    ] = 'smf',
                ):
        super().__init__()
        
        self.preprocess_config_x = preprocess_config_x
        self.encoder_config_x = encoder_config_x
        self.encoder_config_y = encoder_config_y
        self.decoder_config = decoder_config
        self.feature_positional_embedding_type = feature_positional_embedding_type
        self.classify_reg_mixed = classify_reg_mixed
        self.nlayers = nlayers
        self.nhead = nhead
        self.embed_dim = embed_dim
        self.hid_dim = hid_dim
        self.mask_feature_embedding_type = mask_feature_embedding_type
        self.enable_mask_feature_pred = enable_mask_feature_pred
        self.enable_mask_indicator_pred = enable_mask_indicator_pred
        self.features_per_group = features_per_group
        self.dropout = dropout
        self.pre_norm = pre_norm
        self.activation = activation
        self.layer_norm_eps = layer_norm_eps
        self.device = device
        self.dtype = dtype
        self.recompute_attn = recompute_attn
        self.qkv_w_init_method = qkv_w_init_method
        self.mlp_init_std = mlp_init_std
        self.mlp_use_residual = mlp_use_residual
        self.layer_arch = layer_arch

        layer_creator = lambda: EncoderBaseLayer(
            embed_dim=self.embed_dim,
            hid_dim=self.hid_dim,
            nhead=self.nhead,
            dropout=self.dropout,
            pre_norm=self.pre_norm,
            activation=self.activation, # type: ignore
            layer_norm_eps=self.layer_norm_eps,
            device=self.device,
            dtype=self.dtype,
            recompute_attn=self.recompute_attn,
            qkv_w_init_method=self.qkv_w_init_method, # type: ignore
            mlp_init_std=self.mlp_init_std,
            mlp_use_residual=self.mlp_use_residual,
            layer_arch=self.layer_arch, # type: ignore
        )

        self.encoder_x = get_x_encoder( **encoder_config_x)
        self.cls_y_encoder = get_cls_y_encoder(**encoder_config_y)
        self.reg_y_encoder = get_reg_y_encoder(**encoder_config_y)

        self.transformer_encoder = nn.Sequential( *[layer_creator() for _ in range(self.nlayers)])
        self.encoder_out_norm = nn.LayerNorm(self.embed_dim, eps=1e-5, elementwise_affine=False) if pre_norm else nn.Identity()

        self.cls_y_decoder = nn.Sequential(
                                            nn.Linear(self.embed_dim, self.hid_dim),
                                            nn.GELU(),
                                            nn.Linear(self.hid_dim, decoder_config['num_classes']),
                                            )
        if self.classify_reg_mixed:
            self.reg_y_decoder = nn.Sequential(
                                            nn.Linear(self.embed_dim, self.hid_dim),
                                            nn.LayerNorm(self.hid_dim),
                                            nn.GELU(),
                                            nn.Linear(self.hid_dim, 1),
                                            )
        if self.enable_mask_feature_pred:
            self.feature_decoder = nn.Sequential(
                                            nn.Linear(self.embed_dim, self.hid_dim),
                                            nn.LayerNorm(self.hid_dim),
                                            nn.GELU(),
                                            nn.Linear(self.hid_dim, self.features_per_group),
                                            )
        if self.enable_mask_indicator_pred:
            self.mask_indicator_decoder = nn.Sequential(
                                                    nn.Linear(self.embed_dim, self.hid_dim // 2),
                                                    nn.LayerNorm(self.hid_dim // 2),
                                                    nn.GELU(),
                                                    nn.Linear(self.hid_dim // 2, self.features_per_group),
                                                )
        if feature_positional_embedding_type == "learned":
            self.feature_positional_embedding = nn.Embedding(1_000, self.embed_dim)
        elif feature_positional_embedding_type == "subspace":
            self.feature_positional_embedding = nn.Linear(self.embed_dim // 4, self.embed_dim)
        elif feature_positional_embedding_type == "subortho":
            self.feature_positional_embedding = nn.Linear(self.embed_dim // 4, self.embed_dim)
        
        self.x_preprocess = preprocesss_4_x(**preprocess_config_x)
        
        self._init_weights()
        
    def _init_weights(self):
        """ Feature decoder weight initialization"""
        if self.enable_mask_feature_pred:
            for module in self.feature_decoder:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    nn.init.constant_(module.bias, 0)
        
        # Mask解码器初始化
        if self.enable_mask_indicator_pred:
            for module in self.mask_indicator_decoder:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    nn.init.constant_(module.bias, 0)
            nn.init.constant_(self.mask_indicator_decoder[-1].bias, 0.0)
            
    def forward(self, x:torch.Tensor, y:torch.Tensor, eval_pos:int, x_mask:torch.Tensor=None, task_type:Literal['reg', 'cls']='cls') ->torch.Tensor | dict[str, torch.Tensor]:
        '''
            x: The input x, which includes both train x and test x, Shape: [batch, sequence, feature]
            y: The input y, which includes both train y and test y, Shape: [batch, label]
            eval_pos: Train x and train y split point
            x_mask: Mask the specified features in x, Used during training
            task_type: Type of task, options: cls(classification), reg(regression)
        '''
        assert x is not None and y is not None, "x and y must not be none"
        assert eval_pos > 0, "eval_pos must be a positive number"
        assert len(x.shape)==3, "x must be [Batch, seq, Feature]"
        assert len(y.shape)==2, "y must be [Batch, label]"
        assert eval_pos < x.shape[1] and eval_pos <= y.shape[1], "The split point between train x and test x must be less than the feature dimension of x, and less than or equal to the label dimension of y"
        
        if task_type == 'cls':
            assert self.classify_reg_mixed, "Regression mode requires classify_reg_mixed=True at model initialization"
        
        batch_size, seq_len, num_feature = x.shape
        x = {'data':x, 'mask':x_mask}
        y = {'data':y}
        
        feature_to_add = num_feature%self.features_per_group
        if feature_to_add > 0:
            # Extend the feature dimension of x when it is insufficient
            for k in x:
                x[k] = torch.cat(
                    (
                        x[k],
                        torch.zeros(
                            batch_size,
                            seq_len,
                            feature_to_add,
                            device=x[k].device,
                            dtype=x[k].dtype
                        )
                    ),
                    dim=-1
                )
        for k in x:
            x[k] = x[k].reshape(batch_size, seq_len, x[k].shape[2]/self.features_per_group, self.features_per_group)
        x['eval_pos'] = eval_pos
        preprocessed_x = self.x_preprocess(x)
        real_x = preprocessed_x['data'].clone().detach()
        preprocessed_x = self.process_4_x(preprocessed_x, self.mask_feature_embedding_type, seq_len, batch_size)
        x_encoder_result = self.encoder_x(preprocessed_x)
        x_emb_result = x_encoder_result['data']
        
        for k in y:
            # Extend the label dimension of y when it is insufficient
            y[k] = y[k].unsqueeze(-1)
            if y[k].shape[1] < x['data'].shape[1]:
                y[k] = torch.cat(
                    (
                        y[k],
                        torch.nan
                        * torch.zeros(
                            y[k].shape[0],
                            x["data"].shape[1] - y[k].shape[1],
                            y[k].shape[2],
                            device=y[k].device,
                            dtype=y[k].dtype,
                        ),
                    ),
                    dim=1
                )
        # Mask the test y
        y["data"][eval_pos:] = torch.nan
        
        if task_type == 'cls':
            y_type =  torch.zeros_like(y['data'], device=y['data'].device)
        else:
            y_type =  torch.ones_like(y['data'], device=y['data'].device)
            
        if self.classify_reg_mixed:
            embedded_y = self.mixed_y_embedding(y, y_type=y_type, eval_pos=eval_pos)
        else:
            y['eval_pos'] = eval_pos
            embedded_y = self.cls_y_encoder(y)
            embedded_y = embedded_y['data']
        if torch.isnan(embedded_y).any():
            raise ValueError("embedded_y contains NaN values; please add a NanEncoder in the encoder")
        
        embedded_x = self.add_embeddings(x_emb_result)
        embedded_all = torch.cat((embedded_x, embedded_y.unsqueeze(2)), dim=2)
        if torch.isnan(embedded_all).any():
            raise ValueError("embedded_all contains NaN values; please add a NanEncoder in the encoder")
        
        encoder_out = self.transformer_encoder(embedded_all, feature_atten_mask=None, eval_pos=eval_pos)
        encoder_out = self.encoder_out_norm(encoder_out)
        
        test_encoder_out = encoder_out[:, eval_pos:, -1]
        test_y_type = y_type[:,eval_pos:]
        encoder_out_4_feature = encoder_out[:, :, :-1, :]
        if self.training and (self.enable_mask_feature_pred or self.enable_mask_indicator_pred):
            cls_output, reg_output = self.y_decoder(test_encoder_out, test_y_type)
            feature_pred, mask_logits = self.mask_feature_decoder(encoder_out_4_feature)
            output_decoded = {
                "cls_output": cls_output,
                "reg_output": reg_output,
                "feature_pred": feature_pred,
                "mask_indicator_pred": mask_logits,
                "real_x": real_x,
            }
            
        else:
            cls_output, reg_output = self.y_decoder(test_encoder_out, test_y_type)
            if task_type=="cls":
                output_decoded = cls_output
            else:
                output_decoded = reg_output
            
        return output_decoded

    
    def mixed_y_embedding(self, y:dict, y_type:torch.Tensor, eval_pos:int):
        y = y['data']
        seq_len, batch_size, y_num = y.shape
        y_flat = y.reshape(-1)
        y_type_flat = y_type.reshape(-1)
        
        idx = torch.arange(len(y_flat), device=y.device)
        idx_cls = idx[y_type_flat == 0]
        idx_reg = idx[y_type_flat == 1]
        y_cls = y_flat[idx_cls]
        y_reg = y_flat[idx_reg]

        y_cls = y_cls.reshape(seq_len, -1, y_num)
        y_reg = y_reg.reshape(seq_len, -1, y_num)
        y_cls = {'data': y_cls, 'eval_pos':eval_pos}
        y_reg = {'data': y_reg, 'eval_pos':eval_pos}

        cls_y_emb = self.cls_y_encoder(y_cls) if len(idx_cls) > 0 else None
        reg_y_emb = self.reg_y_encoder(y_reg) if len(idx_reg) > 0 else None
        
        emb_size = self.ninp
        out = torch.empty(len(y_flat), emb_size, dtype=torch.float16, device=y_flat.device)
        if cls_y_emb is not None:            
            cls_y_emb_flat = cls_y_emb.reshape(-1, emb_size)
            out.index_put_((idx_cls,), cls_y_emb_flat)

        if reg_y_emb is not None:
            reg_y_emb_flat = reg_y_emb.reshape(-1, emb_size).to(torch.float16)
            out.index_put_((idx_reg,), reg_y_emb_flat)

        output = out.reshape(seq_len, batch_size, emb_size)
        return output
    
    def process_4_x(self, data:dict, embedding_type:str, seq_len:int, batch_size:int):
        x_input = data['data']
        mask = data['data'].to(torch.bool)
        ori_shape = mask.shape
        x_input = torch.where(mask, float('nan'), x_input)
        if 'mask_embedding' != embedding_type:
            tmp_mask = mask.reshape(seq_len, batch_size, -1)
            x_input = x_input.reshape(seq_len, batch_size, -1)
            x_feature_mean = torch.nanmean(x_input, dim=(0, 1), keepdim=True)
            x_feature_mean = torch.where(torch.isnan(x_feature_mean), 0, x_feature_mean)  # 全为 nan 的特征直接用 0 替代
            x_input = torch.where(tmp_mask, x_feature_mean, x_input)
            x_input = x_input.reshape(*ori_shape)
        data['data'] = x_input
        return data
    
    def add_embeddings(self, x:torch.Tensor):
        if self.feature_positional_embedding_type == "learned":
            w = self.feature_positional_embedding.weight
            embs = w[
                    torch.randint(
                        0,
                        w.shape[0],
                        (x.shape[2],),
                    )
            ]
            x += embs[None, None]
        elif self.feature_positional_embedding_type == "subspace":
            embs = torch.randn(
                (x.shape[2], x.shape[3] // 4),
                device=x.device,
                dtype=x.dtype,
            )
            embs = self.feature_positional_embedding(embs)
            x += embs[None, None]
        elif self.feature_positional_embedding_type == "orthogonal":
            with torch.cuda.amp.autocast(enabled=False):
                embs = torch.empty(
                    (x.shape[2], x.shape[3]),
                    device=x.device,
                    dtype=torch.float32,
                )
                torch.nn.init.orthogonal_(embs)
            x += embs[None, None].to(x.dtype)

        elif self.feature_positional_embedding_type == "subortho":
            with torch.cuda.amp.autocast(enabled=False):
                embs = torch.randn(
                    (x.shape[2], x.shape[3] // 4),
                    device=x.device,
                    dtype=torch.float32,
                )
                torch.nn.init.orthogonal_(embs)
            embs =self.feature_positional_embedding(embs.to(x.dtype))
            x += embs[None, None]
        elif self.feature_positional_embedding_type is None:
            embs = None
        else:
            raise ValueError(f"Unknown feature_positional_embedding_type={self.feature_positional_embedding_type}")
        return x
    
    def y_decoder(self, test_encoder_out, test_y_type):
        if not self.classify_reg_mixed:
            cls_y = self.cls_y_decoder(test_encoder_out)
            reg_y = None
            return cls_y, reg_y
        
        seq_len, batch_size, emb_size = test_encoder_out.shape
        flat_test_encoder_out = test_encoder_out.reshape(-1, emb_size)
        flat_test_y_type = test_y_type.reshape(-1)
        
        idx = torch.arange(len(flat_test_encoder_out), device=flat_test_encoder_out.device)
        idx_cls = idx[flat_test_y_type == 0]
        idx_reg = idx[flat_test_y_type == 1]

        cls_y_encoder_out = flat_test_encoder_out[idx_cls]
        reg_y_encoder_out = flat_test_encoder_out[idx_reg]
        cls_y_encoder_out = cls_y_encoder_out.reshape(seq_len, -1, emb_size)
        reg_y_encoder_out = reg_y_encoder_out.reshape(seq_len, -1, emb_size)

        cls_y = self.cls_y_decoder(cls_y_encoder_out)
        reg_y = self.reg_y_decoder(reg_y_encoder_out)

        return cls_y, reg_y
    
    def mask_feature_decoder(self, x_encoding):
        feature_pred, mask_logits = None, None
        if self.enable_mask_feature_pred:
            feature_pred = self.feature_decoder(x_encoding)
        if self.enable_mask_indicator_pred:
            mask_logits = self.mask_indicator_decoder(x_encoding)
        return feature_pred, mask_logits