
from maskmodel.encoders import (
    SequentialEncoder,
    InputNormalizationEncoderStep,
    NanHandlingEncoderStep,
    NanFillEncoderStep,
    RemoveDuplicateFeaturesEncoderStep,
    RemoveEmptyFeaturesEncoderStep,
    VariableNumFeaturesEncoderStep,
)


def preprocesss_4_x(
    *,
    num_features: int,
    remove_empty_features: bool,
    remove_duplicate_features: bool,
    nan_handling_enabled: bool,
    normalize_on_train_only: bool,
    normalize_to_ranking: bool,
    normalize_x: bool,
    remove_outliers: bool,
    normalize_by_used_features: bool,
    feature_embedding_type: str = 'line_layer',
    prob_have_nan: float = 0.2,
    ):
    """feature 预处理"""
    inputs_to_merge = {"main": {"dim": num_features}}

    preprocess_steps = []
    if remove_empty_features: 
        # 移除无效（常量）数据
        preprocess_steps += [RemoveEmptyFeaturesEncoderStep()]   

    if remove_duplicate_features:
        # 移除重复特征（未实现）
        preprocess_steps += [RemoveDuplicateFeaturesEncoderStep()]

    preprocess_steps += [NanHandlingEncoderStep(keep_nans=nan_handling_enabled, prob_have_nan=prob_have_nan)]   # 获取 feature 为 nan 和 inf 的特征位置，并将这些特征用同一类特征的均值替代
    
    if nan_handling_enabled:
        inputs_to_merge["nan_indicators"] = {"dim": num_features}
        preprocess_steps += [
            # 通过添加零值将输入转换为固定数量的特征，不归一化处理（方差不恒定），是对nan_indicators组做的，它的形状和x一样，但x在做这一步之前已经做了填充，所以理论上这一步没用作用
            VariableNumFeaturesEncoderStep(
                num_features=num_features,
                normalize_by_used_features=False,
                in_keys=["nan_indicators"],
                out_keys=["nan_indicators"],
            ),
        ]

    preprocess_steps += [
        # 数据归一化
        InputNormalizationEncoderStep(
            normalize_on_train_only=normalize_on_train_only,
            normalize_to_ranking=normalize_to_ranking,
            normalize_x=normalize_x,
            remove_outliers=remove_outliers,
        ),
    ]

    preprocess_steps += [
        # 通过添加零值将输入转换为固定数量的特征，归一化处理（方差恒定）
        VariableNumFeaturesEncoderStep(
            num_features=num_features,
            normalize_by_used_features=normalize_by_used_features,
        ),
    ]

    return SequentialEncoder(*preprocess_steps, output_key=["main", "nan_indicators", "mask"])
