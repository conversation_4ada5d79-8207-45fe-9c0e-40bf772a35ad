#!/usr/bin/env python3

"""
Simple test for AutoGluon classifier to debug hanging issues
"""

import pandas as pd
import numpy as np
from sklearn.datasets import make_classification
from model_registry import get_model_class

def test_autogluon_simple():
    print("Creating simple test data...")
    
    # Create a small test dataset
    X, y = make_classification(
        n_samples=1000, 
        n_features=10, 
        n_classes=2, 
        random_state=42
    )
    
    # Convert to DataFrame
    X_df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
    
    print(f"Data shape: {X_df.shape}")
    print(f"Target distribution: {np.bincount(y)}")
    
    try:
        print("Getting AutoGluon classifier...")
        autogluon_class = get_model_class('autogluon_classifier')
        classifier = autogluon_class(gpu_id=None)
        
        print("Starting fit...")
        classifier.fit(X_df, y)
        
        print("✓ Fit completed successfully!")
        
        # Test prediction
        print("Testing prediction...")
        roc, ce, acc, balanced_acc = classifier.predict(X_df[:100], y[:100])
        
        print(f"✓ Prediction completed!")
        print(f"ROC: {roc:.4f}")
        print(f"CE: {ce:.4f}")
        print(f"Acc: {acc:.4f}")
        print(f"Balanced Acc: {balanced_acc:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_autogluon_simple()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
