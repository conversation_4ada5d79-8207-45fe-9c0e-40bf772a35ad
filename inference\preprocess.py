import torch
import numpy as np
import warnings
import scipy
from typing_extensions import override
from typing import Literal, NamedTuple, Any
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import (
    OneHotEncoder,
    OrdinalEncoder,
    FunctionTransformer,
    PowerTransformer,
    StandardScaler,
    QuantileTransformer
)
from sklearn.pipeline import FeatureUnion, Pipeline
from sklearn.impute import SimpleImputer
from sklearn.decomposition import TruncatedSVD
from sklearn.utils.validation import check_is_fitted

MAXINT_RANDOM_SEED = int(np.iinfo(np.int32).max)

class SelectiveInversePipeline(Pipeline):
    def __init__(self, steps, skip_inverse=None):
        super().__init__(steps)
        self.skip_inverse = skip_inverse or []
    
    def inverse_transform(self, X):
        """跳过指定步骤的inverse_transform"""
        if X.shape[1] == 0:
            return X
        for step_idx in range(len(self.steps) - 1, -1, -1):
            name, transformer = self.steps[step_idx]
            try:
                check_is_fitted(transformer)
            except:
                continue
            
            # 如果这个步骤在跳过列表中，直接传递X到前一步
            if name in self.skip_inverse:
                continue
                
            if hasattr(transformer, 'inverse_transform'):
                X = transformer.inverse_transform(X)
                if np.any(np.isnan(X)):
                    print(f"After reverse RebalanceFeatureDistribution of {name}, there is nan")
        return X

class RobustPowerTransformer(PowerTransformer):
    """PowerTransformer with automatic feature reversion when variance or value constraints fail."""

    def __init__(self, var_tolerance: float = 1e-3,
                 max_abs_value: float = 100,
                 **kwargs: Any) -> None:
        # 调用基类构造
        super().__init__(**kwargs)
        self.var_tolerance = var_tolerance
        self.max_abs_value = max_abs_value
        self.restore_indices_: np.ndarray | None = None

    # -------------------------
    # 关键：重写 fit
    # -------------------------
    def fit(self, X, y=None):
        # fit 时必须走到派生类的逻辑
        fitted = super().fit(X, y)
        # 记录特征数，初始化为空
        self.restore_indices_ = np.array([], dtype=int)
        return fitted

    def fit_transform(self, X, y=None):
        Z = super().fit_transform(X,y)
        self.restore_indices_ = self._should_revert(Z)
        return Z

    def _should_revert(self, Z: np.ndarray) -> np.ndarray:
        """计算哪些列需要回退."""
        # 1. 方差远离 1
        variances = np.nanvar(Z, axis=0)
        bad_var = np.flatnonzero(np.abs(variances - 1.0) > self.var_tolerance)

        # 2. 出现过大值
        bad_large = np.flatnonzero(np.any(Z > self.max_abs_value, axis=0))

        # 合并并去重
        return np.unique(np.concatenate([bad_var, bad_large]))

    def _apply_reversion(self, Z: np.ndarray, X: np.ndarray) -> np.ndarray:
        if self.restore_indices_.size > 0:
            Z[:, self.restore_indices_] = X[:, self.restore_indices_]
        return Z

    def transform(self, X):
        Z = super().transform(X)
        # self.restore_indices_ = self._should_revert(Z)
        return self._apply_reversion(Z, X)

    def _yeo_johnson_optimize(self, x: np.ndarray) -> float:
        "Overload_yeo_johnson_optimize to avoid crashes caused by values such as NaN and Inf."
        try:
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore",
                                        message=r"overflow encountered",
                                        category=RuntimeWarning)
                return super()._yeo_johnson_optimize(x)  # type: ignore
        except scipy.optimize._optimize.BracketError:
            return np.nan

    def _yeo_johnson_transform(self, x: np.ndarray, lmbda: float) -> np.ndarray:
        "_yeo_johnson_transform to avoid crashes caused by NaN"
        if np.isnan(lmbda):
            return x
        return super()._yeo_johnson_transform(x, lmbda)  # type: ign

class BasePreprocess:
    """Abstract base class for preprocessing class"""

    def fit(self, x:np.ndarray, categorical_features:list[int], seed:int)->list[int]:
        """Fit the preprocessing model to the data"""
        raise NotImplementedError
    
    def transform(self, x:np.ndarray)->tuple[np.ndarray, list[int]]:
        """Transform the data using the fitted preprocessing model"""
        raise NotImplementedError
    
    def fit_transform(self, x:np.ndarray, categorical_features:list[int], seed:int)->tuple[np.ndarray, list[int]]:
        """Fit the preprocessing model to the data and transform the data"""
        self.fit(x, categorical_features, seed)
        return self.transform(x)

def infer_random_state(
    random_state: int | np.random.RandomState | np.random.Generator | None,
) -> tuple[int, np.random.Generator]:
    """高效推断随机状态并返回种子和生成器。"""
    if random_state is None:
        np_rng = np.random.default_rng()
        return int(np_rng.integers(0, MAXINT_RANDOM_SEED)), np_rng
        
    if isinstance(random_state, (int, np.integer)):
        return int(random_state), np.random.default_rng(random_state)
        
    if isinstance(random_state, np.random.RandomState):
        seed = int(random_state.randint(0, MAXINT_RANDOM_SEED))
        return seed, np.random.default_rng(seed)
        
    if isinstance(random_state, np.random.Generator):
        return int(random_state.integers(0, MAXINT_RANDOM_SEED)), random_state
        
    raise ValueError(f"无效的random_state {random_state}")

class FilterValidFeatures(BasePreprocess):
    def __init__(self):
        self.valid_features: list[bool] | None = None
        self.categorical_idx: list[int] | None = None
        self.invalid_indices: list[int] | None = None
        self.invalid_features: list[int] | None = None

    @override
    def fit(self,x: np.ndarray, categorical_idx: list[int], seed:int) -> list[int]:
        self.categorical_idx = categorical_idx
        self.valid_features = ((x[0:1, :] == x).mean(axis=0) < 1.0).tolist()
        self.invalid_indices = ((x[0:1, :] == x).mean(axis=0) == 1.0).tolist()
        if not any(self.valid_features):
            raise ValueError("All features are constant! Please check your data.")

        self.categorical_idx = [
            index
            for index, idx in enumerate(np.where(self.valid_features)[0])
            if idx in categorical_idx
        ]

        return self.categorical_idx
    
    @override
    def transform(self,x: np.ndarray) -> tuple[np.ndarray, list[int]]:
        assert self.valid_features is not None, "You must call fit first to get effective_features"
        self.invalid_features = x[:, self.invalid_indices]
        return x[:, self.valid_features], self.categorical_idx

class FeatureShuffler(BasePreprocess):
    """
    特征列重排处理器
    优化版本：使用更高效的实现方式和改进的变量命名
    """

    # Add random_seed parameter to FeatureShuffler __init__
    def __init__(
        self,
        mode: Literal['rotate', 'shuffle'] | None = "shuffle",
        offset: int = 0,
    ):
        super().__init__()
        self.mode = mode
        self.offset = offset
        self.random_seed = None
        self.feature_indices = None
        self.categorical_indices = None
    
    # Ensure consistent random state usage in both classes
    # Update infer_random_state to use the provided random_seed consistently

    @override
    def fit(self, data: np.ndarray, categorical_cols: list[int], seed:int) -> list[int]:
        n_features = data.shape[1]
        self.random_seed = seed
        
        # 使用预分配数组替代np.arange
        indices = np.arange(n_features)
        
        if self.mode == "rotate":
            self.feature_indices = np.roll(indices, self.offset)
        elif self.mode == "shuffle":
            _, rng = infer_random_state(self.random_seed)
            self.feature_indices = rng.permutation(indices)
        elif self.mode is None:
            self.feature_indices = np.arange(n_features)
        else:
            raise ValueError(f"不支持的重排模式: {self.mode}")

        # 使用布尔索引更高效地计算分类特征位置
        is_categorical = np.isin(np.arange(n_features), categorical_cols)
        self.categorical_indices = np.where(is_categorical[self.feature_indices])[0].tolist()
        
        return self.categorical_indices

    @override
    def transform(self, data: np.ndarray, *, is_test: bool = False) -> tuple[np.ndarray, list[int]]:
        if self.feature_indices is None:
            raise RuntimeError("请先调用fit方法初始化")
        if len(self.feature_indices) != data.shape[1]:
            raise ValueError("输入数据特征数与训练时不一致")
        # print("-"*30+"Before feature shuffle"+"-"*30)
        # get_unique_cnt(data)  
        # 使用高级索引一次性完成特征重排
        return data[:, self.feature_indices], self.categorical_indices or []

class CategoricalFeatureEncoder(BasePreprocess):
    """
    分类特征编码器（优化版）
    支持多种编码方式：ordinal, ordinal_shuffled, onehot, numeric, none
    """

    def __init__(
        self,
        encoding_strategy: Literal['ordinal', 'ordinal_strict_feature_shuffled', 'ordinal_shuffled', 'onehot', 'numeric']|None = "ordinal",
    ):
        super().__init__()
        self.encoding_strategy = encoding_strategy
        self.random_seed = None
        self.transformer = None
        self.feature_indices = None
        self.category_mappings = None
        self.categorical_features = None

    @override
    def fit(self, data: np.ndarray, feature_indices: list[int], seed:int) -> list[int]:
        self.random_seed = seed
        self.transformer, self.feature_indices = self._create_transformer(data, feature_indices)
        
        if self.transformer is not None:
            self.transformer.fit(data)
            
            if self.encoding_strategy == "ordinal_shuffled":
                _, rng = infer_random_state(self.random_seed)
                categories = self.transformer.named_transformers_["ordinal_encoder"].categories_
                self.category_mappings = {
                    idx: rng.permutation(len(cat)) 
                    for idx, cat in enumerate(categories)
                }
        
        return self.feature_indices

    @override
    def transform(self, data: np.ndarray, *, is_test: bool = False) -> tuple[np.ndarray, list[int]]:
        if self.transformer is None:
            return data, self.feature_indices or []
        # todo 不生效？
        transformed = self.transformer.transform(data)
        
        if self.category_mappings is not None:
            for col_idx, mapping in self.category_mappings.items():
                col_data = transformed[:, col_idx]
                valid_mask = ~np.isnan(col_data)
                col_data[valid_mask] = mapping[col_data[valid_mask].astype(int)]
                
        return transformed, self.feature_indices

    @override
    def fit_transform(self, data: np.ndarray, categorical_columns: list[int], seed:int) -> tuple[np.ndarray, list[int]]:
        """合并fit和transform操作，调用_create_transformer和fit_transform"""
        self.random_seed = seed
        return self._fit_transform(data, categorical_columns)

    def _fit_transform(
        self,
        X: np.ndarray,
        categorical_features: list[int],
    ) -> tuple[np.ndarray, list[int]]:
        """合并fit和transform操作，处理大数据集时自动降级"""
        ct, categorical_features = self._create_transformer(X, categorical_features)
        if ct is None:
            self.transformer = None
            return X, categorical_features

        _, rng = infer_random_state(self.random_seed)
        # _, rng = infer_random_state(2)

        if self.encoding_strategy.startswith("ordinal"):
            # print("-"*30+"Before fit_transform"+"-"*30)
            # get_unique_cnt(X)              
            Xt = ct.fit_transform(X)
            # print("-"*30+"After fit_transform"+"-"*30)
            # get_unique_cnt(Xt)
            categorical_features = list(range(len(categorical_features)))

            if self.encoding_strategy.endswith("_shuffled"):
                self.category_mappings = {}
                for col_ix in categorical_features:
                    col_cats = len(
                        ct.named_transformers_["ordinal_encoder"].categories_[col_ix],
                    )
                    perm = rng.permutation(col_cats)
                    self.category_mappings[col_ix] = perm
                    
                    # Apply the permutation immediately, like in the old implementation
                    col_data = Xt[:, col_ix]
                    valid_mask = ~np.isnan(col_data)
                    col_data[valid_mask] = perm[col_data[valid_mask].astype(int)].astype(col_data.dtype)

        elif self.encoding_strategy == "onehot":
            Xt = ct.fit_transform(X)
            if Xt.size >= 1_000_000:
                ct = None
                Xt = X
            else:
                categorical_features = list(range(Xt.shape[1]))[
                    ct.output_indices_["one_hot_encoder"]
                ]
        else:
            raise ValueError(
                f"Unknown categorical transform {self.encoding_strategy}",
            )

        self.transformer = ct
        self.categorical_features = categorical_features
        return Xt, categorical_features

    @staticmethod
    def get_least_common_category_count(column: np.ndarray) -> int:
        """计算列中最少出现的类别数量"""
        if len(column) == 0:
            return 0
        return int(np.unique(column, return_counts=True)[1].min())

    def _create_transformer(self, data: np.ndarray, categorical_columns: list[int]) -> tuple[ColumnTransformer | None, list[int]]:
        """创建合适的列转换器"""
        if self.encoding_strategy.startswith("ordinal"):
            suffix = self.encoding_strategy[len("ordinal"):]
            
            # 处理不同后缀的编码策略
            if "feature_shuffled" in suffix: # what does shuffled imply or affect here?
                categorical_columns = [
                    idx for idx in categorical_columns 
                    if self._is_valid_common_category(data[:, idx], suffix)
                ]
            remainder_columns = [idx for idx in range(data.shape[1]) if idx not in categorical_columns]
            self.feature_indices = categorical_columns + remainder_columns
                
            return ColumnTransformer(
                [("ordinal_encoder", OrdinalEncoder(handle_unknown="use_encoded_value", unknown_value=np.nan), categorical_columns)],
                remainder="passthrough"
            ), categorical_columns
            
        elif self.encoding_strategy == "onehot":
            return ColumnTransformer(
                [("one_hot_encoder", OneHotEncoder(drop="if_binary", sparse_output=False, handle_unknown="ignore"), categorical_columns)],
                remainder="passthrough"
            ), categorical_columns
            
        elif self.encoding_strategy in ("numeric", "none"):
            return None, categorical_columns
            
        raise ValueError(f"不支持的编码策略: {self.encoding_strategy}")

    def _is_valid_common_category(self, column: np.ndarray, suffix: str) -> bool:
        """检查列是否符合常见类别条件"""
        min_count = self.get_least_common_category_count(column)
        unique_count = len(np.unique(column))
        
        if "strict_feature_shuffled" in suffix:
            return min_count >= 10 and unique_count < (len(column) // 10)
        return min_count >= 10
    
class RebalanceFeatureDistribution(BasePreprocess):
    def __init__(
        self,
        *,
        worker_tag: Literal['quantile_uniform_10', 'quantile_uniform_5'] | None= "quantile_uniform",
        discrete_flag: bool = False,
        original_flag: bool = False,
        svd_tag: Literal['svd'] | None = None,
    ):
        super().__init__()
        self.worker_tag = worker_tag
        self.discrete_flag =discrete_flag
        self.original_flag = original_flag
        self.random_state = None
        self.svd_tag = svd_tag
        self.worker: Pipeline | ColumnTransformer | None = None

    def fit(self, X: np.ndarray, categorical_features: list[int], seed:int) -> list[int]:
        self.random_state = seed
        n_samples, n_features = X.shape
        worker, self.dis_ix = self._set(n_samples,n_features,categorical_features)
        worker.fit_transform(X) # repeat?
        self.worker = worker
        return self.dis_ix

    def transform(self, X: np.ndarray) -> np.ndarray:
        assert self.worker is not None
        # print("-"*30+"before transform"+"-"*30)
        # get_unique_cnt(X)
        X_copy = self.worker.transform(X)
        # print("-"*30+"After transform"+"-"*30)
        # get_unique_cnt(X_copy)

        return X_copy, self.dis_ix  # type: ignore


    def _set(self,n_samples: int,
        n_features: int,
        categorical_features: list[int],
        ):
        static_seed, rng = infer_random_state(self.random_state)
        all_ix = list(range(n_features))
        workers = []
        cont_ix = [i for i in all_ix if i not in categorical_features]
        if self.original_flag:
            trans_ixs = categorical_features + cont_ix if self.discrete_flag else cont_ix
            workers.append(("original", "passthrough", all_ix))
            dis_ix = categorical_features
        elif self.discrete_flag:
            trans_ixs = all_ix
            dis_ix = categorical_features
        else:
            workers.append(("discrete", "passthrough", categorical_features))
            trans_ixs, dis_ix = cont_ix, list(range(len(categorical_features)))

        if self.worker_tag == "quantile_uniform_10":
            sworker= QuantileTransformer(
                        output_distribution="uniform",
                        n_quantiles=max(n_samples // 10, 2),
                        random_state=static_seed,
                    )
        elif self.worker_tag == "quantile_uniform_5":
            sworker= QuantileTransformer(
                        output_distribution="uniform",
                        n_quantiles=max(n_samples // 5, 2),
                        random_state=static_seed,
                    )
        elif self.worker_tag == "quantile_uniform_all_data":
            sworker= QuantileTransformer(
                        output_distribution="uniform",
                        n_quantiles=max(n_samples // 5, 2),
                        random_state=static_seed,
                        subsample=n_samples,
                    )
        elif self.worker_tag == 'power':
            self.feature_indices = categorical_features+cont_ix
            self.dis_ix = dis_ix
            nan_to_mean_transformer = SimpleImputer(
                                                missing_values=np.nan,
                                                strategy="mean",
                                                # keep empty features for inverse to function
                                                keep_empty_features=True,
                                            )
            # nan_to_mean_transformer.inverse_transform = lambda x:x
            sworker = SelectiveInversePipeline(
                            steps=[
                                ("power_transformer", RobustPowerTransformer(standardize=False)),
                                ("inf_to_nan_1", FunctionTransformer(
                                                    func=lambda x: np.nan_to_num(x, nan=np.nan, neginf=np.nan, posinf=np.nan),
                                                    inverse_func=lambda x: x,
                                                    check_inverse=False,
                                                )),
                                ("nan_to_mean_1", nan_to_mean_transformer),
                                ("scaler", StandardScaler()),
                                ("inf_to_nan_2", FunctionTransformer(
                                                    func=lambda x: np.nan_to_num(x, nan=np.nan, neginf=np.nan, posinf=np.nan),
                                                    inverse_func=lambda x: x,
                                                    check_inverse=False,
                                                )),
                                ("nan_to_mean_2", nan_to_mean_transformer),
                            ],
                    skip_inverse=['nan_to_mean_1', 'nan_to_mean_2']
            )
        else:
            sworker = FunctionTransformer(lambda x: x)
        if self.worker_tag in ["quantile_uniform_10", "quantile_uniform_5", "quantile_uniform_all_data"]: # TODO: update
            self.n_quantile_features = len(trans_ixs)
        workers.append(("feat_transform", sworker, trans_ixs))

        CT_worker = ColumnTransformer(workers,remainder="drop",sparse_threshold=0.0)
        if self.svd_tag == "svd" and n_features >= 2:
            svd_worker = FeatureUnion([
                    ("default", FunctionTransformer(func=lambda x: x)),
                    ("svd",Pipeline(steps=[ # why repeat? why pipeline in pipeline?
                                    ("save_standard",Pipeline(steps=[
                                    ("i2n_pre", FunctionTransformer(func=lambda x: np.nan_to_num(x, nan=np.nan, neginf=np.nan, posinf=np.nan),inverse_func=lambda x: x, check_inverse=False)),
                                    ("fill_missing_pre", SimpleImputer(missing_values=np.nan, strategy="mean", keep_empty_features=True)),
                                    ("standard", StandardScaler(with_mean=False)) ,
                                    ("i2n_post", FunctionTransformer(func=lambda x: np.nan_to_num(x, nan=np.nan, neginf=np.nan, posinf=np.nan),inverse_func=lambda x: x, check_inverse=False)),
                                    ("fill_missing_post", SimpleImputer(missing_values=np.nan, strategy="mean", keep_empty_features=True))])),
                                    ("svd",TruncatedSVD(algorithm="arpack",n_components=max(1,min(n_samples // 10 + 1,n_features // 2)),random_state=static_seed))]))
                    ])
            self.svd_n_comp = max(1,min(n_samples // 10 + 1,n_features // 2))
            worker = Pipeline([("worker", CT_worker), ("svd_worker", svd_worker)])
        else:   
            worker = CT_worker

        self.worker = worker
        return worker, dis_ix