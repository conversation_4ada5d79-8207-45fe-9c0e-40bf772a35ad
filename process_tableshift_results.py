import pandas as pd
import numpy as np

def process_tableshift_results(input_file, output_file):
    """
    处理tableshift结果文件，计算每个模型在每个数据集上的平均指标和排名
    
    参数:
    input_file: 输入CSV文件路径
    output_file: 输出CSV文件路径
    """
    
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 提取数据集名称（去掉repeat_x后缀）
    df['dataset_base'] = df['dataset name'].str.replace(r'_repeat_\d+$', '', regex=True)
    
    # 获取所有模型名称（从列名中提取）
    model_columns = []
    for col in df.columns:
        if col.endswith('_classifier'):
            model_name = col.replace('_classifier', '')
            if model_name not in model_columns:
                model_columns.append(model_name)
    
    # 为每个数据集和模型组合计算结果
    results = []
    
    # 获取所有唯一的数据集
    datasets = df['dataset_base'].unique()
    
    for dataset in datasets:
        # 获取该数据集的所有重复实验
        dataset_data = df[df['dataset_base'] == dataset].copy()
        
        # 为每个模型计算指标
        for model in model_columns:
            # 构建列名
            id_acc_col = f'id_acc{model}_classifier'
            id_auc_col = f'id_auc{model}_classifier'
            id_ce_col = f'id_ce{model}_classifier'
            ood_acc_col = f'ood_acc{model}_classifier'
            ood_auc_col = f'ood_auc{model}_classifier'
            ood_ce_col = f'ood_ce{model}_classifier'
            
            # 检查列是否存在
            if not all(col in dataset_data.columns for col in [id_acc_col, id_auc_col, id_ce_col, ood_acc_col, ood_auc_col, ood_ce_col]):
                continue
            
            # 提取ID和OOD的指标值
            id_acc_values = dataset_data[id_acc_col].dropna().values
            id_auc_values = dataset_data[id_auc_col].dropna().values
            id_ce_values = dataset_data[id_ce_col].dropna().values
            ood_acc_values = dataset_data[ood_acc_col].dropna().values
            ood_auc_values = dataset_data[ood_auc_col].dropna().values
            ood_ce_values = dataset_data[ood_ce_col].dropna().values
            
            # 跳过没有数据的情况
            if len(id_acc_values) == 0 or len(ood_acc_values) == 0:
                continue
            
            # 计算平均值
            mean_id_acc = np.mean(id_acc_values)
            mean_id_auc = np.mean(id_auc_values)
            mean_id_ce = np.mean(id_ce_values)
            mean_ood_acc = np.mean(ood_acc_values)
            mean_ood_auc = np.mean(ood_auc_values)
            mean_ood_ce = np.mean(ood_ce_values)
            
            # 计算每次重复的排名，然后取平均
            # 对于每次重复，计算该模型在所有模型中的排名
            id_acc_ranks = []
            id_auc_ranks = []
            id_ce_ranks = []
            ood_acc_ranks = []
            ood_auc_ranks = []
            ood_ce_ranks = []
            
            for idx, row in dataset_data.iterrows():
                # 获取该次重复中所有模型的指标值
                repeat_id_acc = []
                repeat_id_auc = []
                repeat_id_ce = []
                repeat_ood_acc = []
                repeat_ood_auc = []
                repeat_ood_ce = []
                
                for m in model_columns:
                    id_acc_c = f'id_acc{m}_classifier'
                    id_auc_c = f'id_auc{m}_classifier'
                    id_ce_c = f'id_ce{m}_classifier'
                    ood_acc_c = f'ood_acc{m}_classifier'
                    ood_auc_c = f'ood_auc{m}_classifier'
                    ood_ce_c = f'ood_ce{m}_classifier'
                    
                    if all(col in row.index for col in [id_acc_c, id_auc_c, id_ce_c, ood_acc_c, ood_auc_c, ood_ce_c]):
                        if not pd.isna(row[id_acc_c]):
                            repeat_id_acc.append((m, row[id_acc_c]))
                            repeat_id_auc.append((m, row[id_auc_c]))
                            repeat_id_ce.append((m, row[id_ce_c]))
                            repeat_ood_acc.append((m, row[ood_acc_c]))
                            repeat_ood_auc.append((m, row[ood_auc_c]))
                            repeat_ood_ce.append((m, row[ood_ce_c]))
                
                # 计算排名（ACC和AUC越高越好，CE越低越好）
                if repeat_id_acc:
                    # ID ACC排名（降序，值越大排名越靠前）
                    repeat_id_acc.sort(key=lambda x: x[1], reverse=True)
                    for rank, (m, _) in enumerate(repeat_id_acc, 1):
                        if m == model:
                            id_acc_ranks.append(rank)
                            break
                    
                    # ID AUC排名（降序）
                    repeat_id_auc.sort(key=lambda x: x[1], reverse=True)
                    for rank, (m, _) in enumerate(repeat_id_auc, 1):
                        if m == model:
                            id_auc_ranks.append(rank)
                            break
                    
                    # ID CE排名（升序，值越小排名越靠前）
                    repeat_id_ce.sort(key=lambda x: x[1])
                    for rank, (m, _) in enumerate(repeat_id_ce, 1):
                        if m == model:
                            id_ce_ranks.append(rank)
                            break
                    
                    # OOD ACC排名（降序）
                    repeat_ood_acc.sort(key=lambda x: x[1], reverse=True)
                    for rank, (m, _) in enumerate(repeat_ood_acc, 1):
                        if m == model:
                            ood_acc_ranks.append(rank)
                            break
                    
                    # OOD AUC排名（降序）
                    repeat_ood_auc.sort(key=lambda x: x[1], reverse=True)
                    for rank, (m, _) in enumerate(repeat_ood_auc, 1):
                        if m == model:
                            ood_auc_ranks.append(rank)
                            break
                    
                    # OOD CE排名（升序）
                    repeat_ood_ce.sort(key=lambda x: x[1])
                    for rank, (m, _) in enumerate(repeat_ood_ce, 1):
                        if m == model:
                            ood_ce_ranks.append(rank)
                            break
            
            # 计算平均排名
            mean_id_acc_rank = np.mean(id_acc_ranks) if id_acc_ranks else np.nan
            mean_id_auc_rank = np.mean(id_auc_ranks) if id_auc_ranks else np.nan
            mean_id_ce_rank = np.mean(id_ce_ranks) if id_ce_ranks else np.nan
            mean_ood_acc_rank = np.mean(ood_acc_ranks) if ood_acc_ranks else np.nan
            mean_ood_auc_rank = np.mean(ood_auc_ranks) if ood_auc_ranks else np.nan
            mean_ood_ce_rank = np.mean(ood_ce_ranks) if ood_ce_ranks else np.nan
            
            # 计算总体平均排名（所有指标的平均）
            all_ranks = [r for r in [mean_id_acc_rank, mean_id_auc_rank, mean_id_ce_rank, 
                                   mean_ood_acc_rank, mean_ood_auc_rank, mean_ood_ce_rank] if not pd.isna(r)]
            mean_overall_rank = np.mean(all_ranks) if all_ranks else np.nan
            
            # 添加ID结果
            results.append({
                'dataset': dataset,
                'split': 'id',
                'model_name': model,
                'mean_acc': mean_id_acc,
                'mean_auc': mean_id_auc,
                'mean_ce': mean_id_ce,
                'mean_rank': np.mean([mean_id_acc_rank, mean_id_auc_rank, mean_id_ce_rank]) if all(not pd.isna(r) for r in [mean_id_acc_rank, mean_id_auc_rank, mean_id_ce_rank]) else np.nan
            })
            
            # 添加OOD结果
            results.append({
                'dataset': dataset,
                'split': 'ood',
                'model_name': model,
                'mean_acc': mean_ood_acc,
                'mean_auc': mean_ood_auc,
                'mean_ce': mean_ood_ce,
                'mean_rank': np.mean([mean_ood_acc_rank, mean_ood_auc_rank, mean_ood_ce_rank]) if all(not pd.isna(r) for r in [mean_ood_acc_rank, mean_ood_auc_rank, mean_ood_ce_rank]) else np.nan
            })
    
    # 转换为DataFrame并保存
    results_df = pd.DataFrame(results)
    results_df = results_df.round(4)  # 保留4位小数
    results_df.to_csv(output_file, index=False)
    
    print(f"处理完成！结果已保存到 {output_file}")
    print(f"共处理了 {len(datasets)} 个数据集，{len(model_columns)} 个模型")
    print(f"生成了 {len(results_df)} 行结果")
    
    return results_df

if __name__ == "__main__":
    # 处理文件
    input_file = "tableshift_all_rsts.csv"
    output_file = "tableshift_processed_results.csv"
    
    results = process_tableshift_results(input_file, output_file)
    
    # 显示前几行结果
    print("\n前10行结果预览：")
    print(results.head(10))
    
    # 显示数据集和模型统计
    print(f"\n数据集列表：")
    print(results['dataset'].unique())
    print(f"\n模型列表：")
    print(results['model_name'].unique())
