from typing import Dict, Callable
# from causalBench.models import LogisticClassifier, XGBoostClassifier

import pandas as pd


import numpy as np
from sklearn.inspection import permutation_importance



# 全局模型注册表
_model_registry: Dict[str, Callable] = {}
# model name -> parameters path dict
_model_param_paths: Dict[str, str] = { # TODO change to real paths
    'tabpfn_classifier': '/root/TabPFN-v2/tabpfn-v2-classifier.ckpt',
    'ldm_classifier': '/root/TabPFN-v2/1549_930.cpkt',
    'ldm16m_classifier': '/root/TabPFN-v2/16m_830.cpkt',
    'tabicl_classifier': '/mnt/public/lxy/models/tabicl-classifier-v1-0506.ckpt',
    'tabdpt_classifier': '/mnt/public/lxy/models/tabdpt1_1.safetensors',
}
_use_gpu: Dict[str, bool] = {
    'logistic_classifier': False,
    'xgboost_classifier': False,
    'catboost_classifier': False,
    'autogluon_classifier': True,
    'tabpfn_classifier': True,
    'ldm_classifier':True,
    'ldm16m_classifier':True,
    'tabicl_classifier': True,
    'tabdpt_classifier': True,
    'fttransformer_classifier': True,
}

def register_model(model_name: str):
    """装饰器：注册模型及其三个步骤函数"""
    def decorator(cls):
        _model_registry[model_name] = cls
        return cls
    return decorator

def get_registered_models():
    """获取已注册的模型列表"""
    return list(_model_registry.keys())

def get_model_class(model_name: str):
    """获取指定模型的类"""
    if model_name not in _model_registry:
        raise ValueError(f"Model {model_name} not registered.")
    return _model_registry[model_name]

def get_model_steps(model_name: str):
    """获取指定模型的步骤函数"""
    if model_name not in _model_registry:
        raise ValueError(f"Model {model_name} not registered.")
    return _model_registry[model_name]

#### Final Code Snippet ####

#### Model Implementations ####
import time
from scripts import tabular_metrics
import os, torch

class ClassifierBase:
    def __init__(self, model, name, device):
        self.model = model
        self.name = name
        self.device = device
        # if gpu_id is not None:
        #     os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        #     torch.cuda.set_device(0) 
        #     print(f"{name} Set CUDA_VISIBLE_DEVICES to: {os.environ['CUDA_VISIBLE_DEVICES']}")
        # else:
        #     print(f"{name} No GPU specified, using CPU.")
        return

    def fit(self, train_x, train_y, **kwargs):
        '''
        train_x: dtype=torch.tensor
        train_y: dtype=torch.tensor
        Some models may change to numpy or df, which is explicit
        '''
        if type(train_x) == torch.tensor:
            train_x = train_x.to(self.device)
            train_y = train_y.to(self.device)
        self.model.fit(train_x, train_y, **kwargs)
        return self.model

    def predict(self, test_xs, test_ys):
        '''
        test_xs: dtype=torch.tensor
        test_ys: dtype=torch.tensor
        Some models may change to numpy or df, which is explicit
        '''
        if type(test_xs) == torch.tensor:
            test_xs = test_xs.to(self.device)
            test_ys = test_ys.to(self.device)
        prediction = self.model.predict_proba(test_xs)
        self.roc = tabular_metrics.auc_metric(test_ys, prediction)
        self.ce = tabular_metrics.cross_entropy(test_ys, prediction)
        self.acc = tabular_metrics.accuracy_metric(test_ys, prediction)
        self.balanced_acc = tabular_metrics.balanced_accuracy_metric(test_ys, prediction)
        return (self.roc, self.ce, self.acc, self.balanced_acc)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return pd.DataFrame({
            'roc': [self.roc],
            'ce': [self.ce],
            'acc': [self.acc],
            'armb': [self.armb],
            'armb_norm': [self.armb_norm],
            'armb_ranks': [self.armb_ranks],
            'high_rate': [self.high_rate],
            'top_m_features': [self.top_m_features],
            'mb_avg_pfi': [self.mb_avg_pfi],
            'non_mb_avg_pfi': [self.non_mb_avg_pfi]
        })


@register_model('logistic_classifier')
class LogisticClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = 'cpu'
        from sklearn.linear_model import LogisticRegression
        super().__init__(LogisticRegression(), "LogisticClassifier", self.device) # use cpu
        return
    
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return

    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)

    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)
    
    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

@register_model('xgboost_classifier')
class XGBoostClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        # device = 'cuda' if gpu_id is not None else 'cpu'
        self.device = 'cpu'
        from xgboost import XGBClassifier
        self.model = XGBClassifier(device=self.device)
        self.name = "XGBoostClassifier"
        super().__init__(self.model, self.name, self.device)
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)
    def rst2df(self):
        return super().rst2df()

@register_model('lgb_classifier')
class LGBClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        # device = 'cuda' if gpu_id is not None else 'cpu'
        self.device = 'cpu'
        import lightgbm as lgb
        self.model = lgb.LGBMClassifier(verbose=-1)
        self.name = "LGBClassifier"
        super().__init__(self.model, self.name, self.device)
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)
    def rst2df(self):
        return super().rst2df()

@register_model('catboost_classifier')
class CatBoostClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = 'cpu'
        from catboost import CatBoostClassifier as catboostClassifier
        self.model = catboostClassifier(               # tree depth
            loss_function='Logloss',# for binary classification
            # loss_function='MultiClass', # for multi-class classification
            verbose=False
            )
        self.name = "CatBoostClassifier"
        super().__init__(self.model, self.name, self.device)
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)
    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()


@register_model('autogluon_classifier')
class AutoGluonClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = 'cpu'  # AutoGluon handles GPU internally
        from autogluon.tabular import TabularPredictor
        # We'll initialize the predictor in fit method since it needs data info
        self.predictor = None
        self.name = "AutoGluonClassifier"
        # Create a dummy model for super().__init__
        class DummyModel:
            def fit(self, X, y, **kwargs):
                pass
            def predict_proba(self, X):
                pass
        super().__init__(DummyModel(), self.name, self.device)

    def fit(self, train_x, train_y):
        """
        train_x: pandas DataFrame
        train_y: numpy array or pandas Series
        """
        from autogluon.tabular import TabularPredictor
        import pandas as pd
        import tempfile
        import os

        # Convert to DataFrame if needed
        if not isinstance(train_x, pd.DataFrame):
            train_x = pd.DataFrame(train_x)
        if not isinstance(train_y, pd.Series):
            train_y = pd.Series(train_y)

        # Combine features and target
        train_data = train_x.copy()
        train_data['target'] = train_y

        # Create temporary directory for AutoGluon
        self.temp_dir = tempfile.mkdtemp()

        # Initialize predictor
        self.predictor = TabularPredictor(
            label='target',
            path=self.temp_dir,
            verbosity=0,  # Reduce verbosity
            eval_metric='roc_auc'
        )

        # Fit the model
        self.predictor.fit(
            train_data,
            time_limit=600, 
            verbosity=0
        )

        return self.predictor

    def predict(self, test_xs, test_ys):
        """
        test_xs: pandas DataFrame
        test_ys: numpy array or pandas Series
        """
        import pandas as pd
        import numpy as np

        # Convert to DataFrame if needed
        if not isinstance(test_xs, pd.DataFrame):
            test_xs = pd.DataFrame(test_xs)
        if not isinstance(test_ys, pd.Series):
            test_ys = pd.Series(test_ys) if hasattr(test_ys, '__len__') else test_ys

        # Get predictions
        prediction_proba = self.predictor.predict_proba(test_xs)

        # Convert to numpy array and ensure 2D format for metrics
        prediction = prediction_proba.values

        # Calculate metrics
        self.roc = tabular_metrics.auc_metric(test_ys, prediction)
        self.ce = tabular_metrics.cross_entropy(test_ys, prediction)
        self.acc = tabular_metrics.accuracy_metric(test_ys, prediction)
        self.balanced_acc = tabular_metrics.balanced_accuracy_metric(test_ys, prediction)

        return (self.roc, self.ce, self.acc, self.balanced_acc)

    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

    def __del__(self):
        """Clean up temporary directory"""
        if hasattr(self, 'temp_dir') and self.temp_dir:
            import shutil
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass


@register_model('tabpfn_classifier')
class TabPFNClassifier(ClassifierBase):
    def __init__(self, gpu_id=None, model_save=_model_param_paths['tabpfn_classifier']):
        from inference.classifier import TabPFNClassifier as tabpfnClassifier
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        self.model = tabpfnClassifier(device=self.device, ignore_pretraining_limits=True, model_path=model_save)
        self.name = "TabPFNClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('ldm_classifier')
class LDMClassifier(ClassifierBase):
    def __init__(self, gpu_id=None, model_save=_model_param_paths['ldm_classifier']):
        from inference_ldm.predictor import LDMPredictor
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        self.model = LDMPredictor(device=self.device, 
                                  model_path=model_save, 
                                  n_estimators=4,
                                  outlier_remove_std=12,
                                  seed=0)
        self.name = "LDMClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        return self.model
    def predict(self, train_xs, train_ys, test_xs, test_ys):
        if type(test_xs) == torch.tensor:
            train_xs = train_xs.to(self.device)
            train_ys = train_ys.to(self.device)
            test_xs = test_xs.to(self.device)
            test_ys = test_ys.to(self.device)
        prediction, _ = self.model.predict(train_xs, train_ys, test_xs)
        self.roc = tabular_metrics.auc_metric(test_ys, prediction)
        self.ce = tabular_metrics.cross_entropy(test_ys, prediction)
        self.acc = tabular_metrics.accuracy_metric(test_ys, prediction)
        self.balanced_acc = tabular_metrics.balanced_accuracy_metric(test_ys, prediction)        
        del prediction
        return (self.roc, self.ce, self.acc, self.balanced_acc)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

@register_model('ldm16m_classifier')
class LDM16MClassifier(ClassifierBase):
    def __init__(self, gpu_id=None, model_save=_model_param_paths['ldm16m_classifier']):
        from inference_ldm.predictor import LDMPredictor
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        self.model = LDMPredictor(device=self.device, 
                                  model_path=model_save, 
                                  n_estimators=4,
                                  outlier_remove_std=12,
                                  seed=0)
        self.name = "LDM16MClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        return self.model
    def predict(self, train_xs, train_ys, test_xs, test_ys):
        if type(test_xs) == torch.tensor:
            train_xs = train_xs.to(self.device)
            train_ys = train_ys.to(self.device)
            test_xs = test_xs.to(self.device)
            test_ys = test_ys.to(self.device)
        prediction, _ = self.model.predict(train_xs, train_ys, test_xs)
        self.roc = tabular_metrics.auc_metric(test_ys, prediction)
        self.ce = tabular_metrics.cross_entropy(test_ys, prediction)
        self.acc = tabular_metrics.accuracy_metric(test_ys, prediction)
        self.balanced_acc = tabular_metrics.balanced_accuracy_metric(test_ys, prediction)        
        del prediction
        return (self.roc, self.ce, self.acc, self.balanced_acc)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

@register_model('tabicl_classifier')
class TabICLClassifier(ClassifierBase):
    def __init__(self, gpu_id=None, model_path=_model_param_paths['tabicl_classifier']):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from tabicl import TabICLClassifier as tabiclClassifier
        self.model = tabiclClassifier(device=self.device, model_path=model_path)
        self.name = "TabICLClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

@register_model('tabdpt_classifier')
class TabDPTClassifier(ClassifierBase):
    def __init__(self, gpu_id=None, model_path=_model_param_paths['tabdpt_classifier']):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from tabdpt import TabDPTClassifier as tabdptClassifier
        self.model = tabdptClassifier(device=self.device, model_path=model_path)
        self.name = "TabDPTClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y)
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(test_xs, test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(test_xs, test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df() 
    
@register_model('fttransformer_classifier')
class FTTransformerClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        # from fttransformer import FTTransformerClassifier
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer import FTTransformerClassifier
        self.model = FTTransformerClassifier()
        self.model = self.model
        self.name = "FTTransformerClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('tabr_classifier')
class TabRClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.tabr import TabRClassifier
        self.model = TabRClassifier()
        self.name = "TabRClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()

@register_model('modernnca_classifier')
class ModernNCAClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.modernnca import ModernNCAClassifier
        self.model = ModernNCAClassifier()
        self.name = "ModernNCAClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('mlp_classifier')
class MLPClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.mlp import MLPClassifier
        self.model = MLPClassifier()
        self.name = "MLPClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('node_classifier')
class NODEClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.node import NODEClassifier
        self.model = NODEClassifier()
        self.name = "NODEClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('resnet_classifier')
class ResNetClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.resnet import ResNetClassifier
        self.model = ResNetClassifier()
        self.name = "ResNetClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('saint_classifier')
class SAINTClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.saint import SAINTClassifier
        self.model = SAINTClassifier()
        self.name = "SAINTClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('tabtransformer_classifier')
class TabTransformerClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.tabtransformer import TabTransformerClassifier
        self.model = TabTransformerClassifier()
        self.name = "TabTransformerClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()
    
@register_model('tangos_classifier')
class TangosClassifier(ClassifierBase):
    def __init__(self, gpu_id=None):
        self.device = f"cuda:{gpu_id}" if gpu_id is not None else "cpu"
        from fttransformer.tangos import TangosClassifier
        self.model = TangosClassifier()
        self.name = "TangosClassifier"
        super().__init__(self.model, self.name, self.device)
        return
    def fit(self, train_x, train_y):
        super().fit(train_x, train_y, max_epochs=50, lr=1e-04, 
                checkpoint_path=None,
                logger=False,       # 禁用所有日志记录器（如 TensorBoard、CSVLogger）
                enable_checkpointing=False,  # 禁用 checkpoint 日志
                enable_progress_bar=False,   # 禁用进度条
                enable_model_summary=False,   # 禁用模型结构摘要
                dataloader_kwargs={"num_workers": 2, "persistent_workers": True}
                )
        return self.model
    def predict(self, test_xs, test_ys):
        return super().predict(pd.DataFrame(test_xs), test_ys)
    def PFI_calc(self, test_xs, test_ys, causal_info, features):
        return super().PFI_calc(pd.DataFrame(test_xs), test_ys, causal_info, features)

    def rst2df(self):
        """将结果转换为DataFrame"""
        return super().rst2df()