#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 
# Filename: inference_kaggle.py
# Author: haoyuan
# Created: 2025-07-15
# Description: This is a simple Python script with header information.
# conda activate PFN
# export OMP_NUM_THREADS="16"
# python ldm_inference_benchmark183.py --model_path '/mnt/public/tabpfn-v2_dev_merge_0425/tabpfn-v2-classifier.ckpt' --gpuID 7 --data_dir '/root/benchmark183/'
# python ldm_inference_benchmark183.py --model_path '/mnt/public/auto_inference/seed_phi_mseed3407_dseed3407_0628_1739/prior_diff_real_checkpoint_n_0_epoch_1000.cpkt' --gpuID 7 --data_dir '/root/benchmark183/'


import os
import pickle
from xgboost import XGBClassifier
# from tabpfn import TabPFNClassifier
# from inference.classifier import TabPFNClassifier
from inference.predictor import LDMPredictor
import pandas as pd
from tqdm import tqdm
import numpy as np
# from scripts import tabular_metrics
import gc
import torch
import argparse  # 新增参数解析模块
from sklearn.model_selection import train_test_split

from sklearn.metrics import accuracy_score
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import roc_auc_score
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import SimpleImputer, IterativeImputer, KNNImputer
from sklearn.compose import ColumnTransformer


def auc_metric(target, pred, multi_class='ovo', numpy=False):
    lib = np if numpy else torch
    try:
        if not numpy:
            target = torch.tensor(target) if not torch.is_tensor(target) else target
            pred = torch.tensor(pred) if not torch.is_tensor(pred) else pred
        if len(lib.unique(target)) > 2:
            if not numpy:
                return torch.tensor(roc_auc_score(target, pred, multi_class=multi_class))
            return roc_auc_score(target, pred, multi_class=multi_class)
        else:
            if len(pred.shape) == 2:
                pred = pred[:, 1]
            if not numpy:
                return torch.tensor(roc_auc_score(target, pred))
            return roc_auc_score(target, pred)
    except ValueError as e:
        print(e)
        return np.nan if numpy else torch.tensor(np.nan)

def gen_nan(x:np.ndarray, drop_percentage):
    if drop_percentage <= 0:
        return x, x, None
    rng = np.random.default_rng(42)
    existing_nan_mask = np.isnan(x)
    valid_positions = np.where(~existing_nan_mask)
    n_valid = len(valid_positions[0])
    assert n_valid > 0
    n_new_missing = int(n_valid * drop_percentage)
    
    indices_to_missing = rng.choice(
        len(valid_positions[0]),  # 从所有有效值索引中选择
        size=n_new_missing,       # 选择的数量
        replace=False             # 无放回抽样
    )
    rows_to_missing = valid_positions[0][indices_to_missing]
    cols_to_missing = valid_positions[1][indices_to_missing] 
    nan_mask = np.zeros_like(x, dtype=np.bool_)
    nan_mask[rows_to_missing, cols_to_missing] = True
    x_original = x.copy()
    x[nan_mask] = np.nan     
    return x, x_original, nan_mask 

def get_categorical_features_indices(x_:np.ndarray):
    min_unique_num_for_numerical_infer = 10
    categories = {}
    x = x_.values if isinstance(x_, pd.DataFrame) else x_ 
    for idx, col in enumerate(x.T):
        if len(np.unique(col)) < min_unique_num_for_numerical_infer:
            categories[idx] = np.unique(col)
    return categories

def mask_prediction_eval(x_pred_:np.ndarray, x_true_:np.ndarray, mask:np.ndarray, categories: dict):
    # There are categorical variables that are not treated as categorical during inference      
    x_pred = x_pred_.copy()
    x_true = x_true_.copy()
    mask_pred_cls_error = -1
    mask_pred_reg_error = -1
    categorical_idx = list(categories.keys())
    for idx in categorical_idx:
        distances = np.abs(x_pred[:,idx][:,np.newaxis]-categories[idx])
        nearest_indices = np.argmin(distances, axis=1)  
        x_pred[:,idx] = categories[idx][nearest_indices]
    cls_error_list = []
    reg_error_list = []
    for idx in range(x_true.shape[1]):
        mask_col = mask[:,idx]
        if idx in categorical_idx:
            cls_error_list.append(x_pred[:,idx][mask_col] != x_true[:,idx][mask_col])
        else:
            reg_error_list.append(x_pred[:,idx][mask_col] - x_true[:,idx][mask_col])
    if cls_error_list != []:
        mask_pred_cls_error = np.concatenate(cls_error_list).mean()
        print("mask_pred_cls_error", mask_pred_cls_error)
    if reg_error_list != []:
        mask_pred_reg_error = np.sqrt(np.mean(np.concatenate(reg_error_list)**2))
        print("mask_pred_rgr_error", mask_pred_reg_error) 
    return mask_pred_cls_error, mask_pred_reg_error   


def BasicImpute(trainX, testX, testX_original, cont_indices, categorical_indices, categories, nan_mask, rst):
    # Basic Imputers from sklearn
    imputers = {
        "Simple": {
            "cont": SimpleImputer(strategy='mean'),
            "cate": SimpleImputer(strategy='most_frequent'),
        },
        "Iterative": IterativeImputer(random_state=0), 
        "KNN": KNNImputer(),
    }
    for imp_name in imputers:
        if imp_name == "Simple":
            preprocessor = ColumnTransformer(
                transformers=[
                    ('num', imputers[imp_name]['cont'], cont_indices),
                    ('cat', imputers[imp_name]['cate'], categorical_indices)
                ],
                remainder='passthrough'
            )
            preprocessor.fit(trainX)
            mask_prediction_ = preprocessor.transform(testX)
            # 由于ColumnTransformer可能会改变列的顺序，我们需要重新排列列以保持原始顺序                            
            transformed_indices = cont_indices + categorical_indices
            original_indices = list(range(testX.shape[1]))
            # 创建映射以恢复原始顺序
            index_mapping = {transformed_indices[i]: i for i in range(len(transformed_indices))}
            reorder_indices = [index_mapping[i] for i in original_indices]
            # 重新排列列以恢复原始顺序
            mask_prediction_ = mask_prediction_[:, reorder_indices]                    
        elif imp_name in ["Iterative", "KNN"]:
            imputers[imp_name].fit(trainX)
            mask_prediction_ = imputers[imp_name].transform(testX)
        else:
            raise NotImplementedError
            
        print("-"*30+f"{imp_name} Imputer"+"-"*30)
        mask_pred_cls_error, mask_pred_reg_error = mask_prediction_eval(mask_prediction_, testX_original, nan_mask, categories) 
        rst[f'mask_pred_cls_error_{imp_name}Imputer'] = mask_pred_cls_error
        rst[f'mask_pred_reg_error_{imp_name}Imputer'] = mask_pred_reg_error    

# 在参数解析部分添加新的参数
if __name__ == '__main__':
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='运行TabPFN评估')
    parser.add_argument('--model_path', type=str, required=True,
                      help='输入模型文件路径，例如 /path/to/model.cpkt')
    parser.add_argument('--gpuID', type=int, default=0,
                      help='指定使用的GPU ID，默认为0')
    parser.add_argument('--mask_prediction', action="store_true",
                      help='是否使用mask prediction')
    parser.add_argument('--drop_percentage', type=float, default=0.0,
                      help='在X中随机产生nan的比例')
    parser.add_argument('--data_dir', type=str, default='./data',
                      help='指定cc18数据集本地存储目录，默认为./data')
    args = parser.parse_args()

    model_file = args.model_path
    gpu_id = int(args.gpuID) % 8  # 从命令行参数获取GPU ID
    data_root = args.data_dir 


    # 动态生成保存路径
    kaggle_results_path = model_file.replace('.cpkt', '_benchmark_results')
    save_root_dir = model_file.split('/')[-3]
    save_root = kaggle_results_path.replace(save_root_dir, save_root_dir+'_ldm183')
    os.makedirs(save_root, exist_ok=True)
    # 新增检查：如果结果文件存在则提前退出
    result_file = os.path.join(save_root, 'all_rst.csv')
    if os.path.exists(result_file):
        print(f"结果文件已存在，跳过执行: {result_file}")
        # exit(0)


    # 初始化归一化器
    scaler = MinMaxScaler()
    # 创建一个标签编码器
    le = LabelEncoder()

    # 读取主CSV文件
    # main_csv_path = os.path.join(data_root, '602_filter.csv')
    # main_csv_path = os.path.join(data_root, 'data_183_sorted_0728.csv')  # 使用新的CSV文件
    # main_csv_path = os.path.join(data_root, 'inhouse_benchmark183.csv')  # 使用新的CSV文件
    main_csv_path = os.path.join(data_root, 'inhouse_diff160_clean.csv')
    # main_csv_path = os.path.join(data_root, '3.csv')
    main_df = pd.read_csv(main_csv_path)

    rsts = []
    model_save = model_file
    if "tabpfn-v2-classifier.ckpt" in model_file:
        model_save = model_file
    else:
        model_save = model_file.replace('.cpkt', '-mfp.cpkt')
        model_ours = model_file
        # model_default = 'tabpfn-v2-classifier.ckpt'
        # try:
        #     with open(model_ours, 'rb') as f:
        #         model_m = torch.load(f)
        #     with open(model_default, 'rb') as f:
        #         model_d = torch.load(f)
        # except Exception as e:
        #     print(f"模型加载失败: {str(e)}")
        #     print(f"请检查文件是否存在: {model_ours} 和 {model_default}")
        #     exit(1)
        with open(model_ours, 'rb') as f:
            model_m = torch.load(f)
        # my_stat = model_m[0]

        if isinstance(model_m, tuple):
            my_stat = model_m[0]
        elif 'state_dict' in model_m:
            my_stat = model_m['state_dict']
        else:
            raise ValueError("不支持的模型权重！")
            
        new_stat = {}
        model_new = {}
        for k, v in my_stat.items():
            if 'criterion' in k:
                continue
            new_stat[k.replace('module.','')] = v
        if isinstance(model_m, tuple):
            model_new[0]=new_stat
            model_new[1]= model_m[1]
            model_new[2]= model_m[2]
            model_new[3]= model_m[3]
        else:
            model_m['state_dict'] = new_stat
            model_new = model_m
            
        # model_m['config']['emsize'] = model_m[2]['emsize']
        # model_m['config']['nhead'] = model_m[2]['nhead']
        # model_m['config']['nlayers'] = model_m[2]['nlayers']
        # model_m['config']['nhid_factor'] = model_m[2]['nhid_factor']
        # model_m['config']['feature_positional_embedding'] = model_m[2].get('feature_positional_embedding', 'subspace') 
        # model_m['config']['pre_norm'] = model_m[2].get('use_pre_norm', False)
        # model_m['config']['use_step_encoder'] = model_m[2].get('use_step_encoder', False)
        # model_m['config']['layer_arch'] = model_m[2].get('layer_arch', 'default')
        torch.save(model_new, open(model_save,'wb'))
        print(model_save, 'model_save, done')


    data_device = f'cuda:{gpu_id}'
    # classifier = TabPFNClassifier(device=data_device, ignore_pretraining_limits=True, model_path=model_save)
    classifier = LDMPredictor(device=data_device, model_path=model_save, n_estimators=4, outlier_remove_std=12, mask_prediction=args.mask_prediction, seed=0)
    # 遍历Folder列  
    for idx, folder in tqdm(enumerate(main_df['dataset name'])):
        # 初始化训练和测试数据
        X_train, X_test, y_train, y_test = None, None, None, None
        folder_path = os.path.join(data_root, folder)

        # print("=============================:"+folder_path)
        try:
        # if True:
            # 查找train.csv和test.csv
            train_path = os.path.join(folder_path, folder+'_train.csv')
            test_path = os.path.join(folder_path, folder+'_test.csv')
            if os.path.exists(train_path):
                train_df = pd.read_csv(train_path)
                
                if os.path.exists(test_path):
                    test_df = pd.read_csv(test_path)
                else:
                    # 如果没有test.csv，将train.csv分为训练集和测试集
                    train_df, test_df = train_test_split(train_df, test_size=0.5, random_state=42)
                    # # 如果测试集超过1万条，将多余样本转移到训练集
                    # if len(test_df) > 10000:
                    #     excess_samples = test_df.iloc[10000:]  # 超出的样本
                    #     test_df = test_df.iloc[:10000].copy()  # 截断测试集
                    #     train_df = pd.concat([train_df, excess_samples], ignore_index=True)  # 添加到训练集
            dataset_name = folder  # 使用文件夹名作为数据集名称

            # 假设最后一列是目标变量
            X_train = train_df.iloc[:, :-1]
            y_train = train_df.iloc[:, -1]
            X_test = test_df.iloc[:, :-1]
            y_test = test_df.iloc[:, -1]
            # from sklearn.preprocessing import LabelEncoder

            # 对每一列进行检查
            for col in X_train.columns:
                if X_train[col].dtype == 'object':  # 检查是否是字符串列
                    try:
                        le = LabelEncoder()
                        X_train[col] = le.fit_transform(X_train[col])
                        X_test[col] = le.transform(X_test[col])  # 确保测试集使用相同的编码
                    except Exception as e:
                        X_train = X_train.drop(columns=[col])
                        X_test = X_test.drop(columns=[col])
            X_train = scaler.fit_transform(X_train)
            X_test = scaler.transform(X_test)

            y_train = le.fit_transform(y_train)
            y_test = le.transform(y_test) 


            trainX, trainy = X_train, y_train
            
            # 新增类型转换代码
            trainX = np.asarray(trainX, dtype=np.float32)
            trainy = np.asarray(trainy, dtype=np.int64)
            
            # break
            #显存限制
            # data_device = 'cpu' if len(trainX) > 20000 else f'cuda:{gpu_id}'
            if len(np.unique(trainy)) > 10 or len(np.unique(trainy)) < 2:
                # print('xxxx', len(np.unique(trainy)))
                continue
            # 过滤掉样本数大于50000的数据集
            if len(trainX) >= 50000:
                # print('x'*100, len(trainX))
                continue
            
            # 修改测试数据加载逻辑
            # if testX is None or testy is None:
            #     l = int(len(trainX)*0.5)
            #     testX = trainX[l:]
            #     testy = trainy[l:]
            #     trainX = trainX[:l]
            #     trainy = trainy[:l]
                
            testX, testy = X_test, y_test
            testX = np.asarray(testX, dtype=np.float32)
            testy = np.asarray(testy, dtype=np.int64)
            
            if args.drop_percentage > 0:
                categories = get_categorical_features_indices(trainX)
                categorical_indices = list(categories.keys())
                cont_indices = [idx for idx in range(testX.shape[1]) if idx not in categories]
                print("trainX.shape:", trainX.shape)
                print("testX.shape:", testX.shape)
                print("Categorical indices:", categorical_indices)
                print("Cont indices:", cont_indices)
                testX, testX_original, nan_mask = gen_nan(testX, args.drop_percentage)
            
            
            # 获取main_df中对应的行数据
            main_row = main_df[main_df['dataset name'] == folder].iloc[0].to_dict()
            
            rst = {
                'dataset name': folder,
                'num_data_train': len(trainX),
                'num_data_test': len(testX),
                'num_feat': len(trainX[0]), 
                'num_class': len(np.unique(trainy)),
                # 添加main_df中的所有字段
                **main_row
            }

            # 新增测试集分块逻辑
            chunk_size = 50000
            roc_values = []
            
            # 当测试集超过1万条时进行分块
            if len(testX) > chunk_size:
                # 将测试集分块
                testX_chunks = np.array_split(testX, np.ceil(len(testX)/chunk_size))
                testy_chunks = np.array_split(testy, np.ceil(len(testy)/chunk_size))
                
                # 遍历每个分块进行预测
                for testX_chunk, testy_chunk in zip(testX_chunks, testy_chunks):
                    # classifier.fit(trainX, trainy)
                    # prediction_ = classifier.predict_proba(testX_chunk)
                    prediction_ = classifier.predict(trainX, trainy, testX_chunk)
                    roc_chunk = auc_metric(testy_chunk, prediction_)
                    roc_values.append(roc_chunk)
                    del prediction_  # 显存释放
                    if 'cuda' in data_device:
                        torch.cuda.empty_cache()
                
                # 计算平均ROC值
                roc = np.mean(roc_values)
            else:
                # 原始处理逻辑
                # classifier.fit(trainX, trainy)
                # prediction_ = classifier.predict_proba(testX)
                if not args.mask_prediction:
                    prediction_, _ = classifier.predict(trainX, trainy, testX)
                else:
                    prediction_, mask_prediction_ = classifier.predict(trainX, trainy, testX)
                    mask_prediction_ = mask_prediction_[-testX.shape[0]:].astype(testX.dtype)
                    print("-"*30+"PFN"+"-"*30)
                    mask_pred_cls_error, mask_pred_reg_error = mask_prediction_eval(mask_prediction_, testX_original, nan_mask, categories) 
                    rst['mask_pred_cls_error_PFN'] = mask_pred_cls_error
                    rst['mask_pred_reg_error_PFN'] = mask_pred_reg_error                    
                    
                    BasicImpute(trainX, testX, testX_original, cont_indices, categorical_indices, categories, nan_mask, rst)
                    
                roc = auc_metric(testy, prediction_)
                del prediction_

            rst['AUC_PFN'] = float(roc)
            rsts.append(rst)
            print(f"[{idx}] {folder} -> {float(roc)}")

        except Exception as e:
            print(f"Error processing: {e}, weight: {model_save}")

            gc.collect()  # 强制进行垃圾回收
            if 'cuda' in data_device:
                torch.cuda.empty_cache()  # 再次确保显存释放
            # exit(0)

        gc.collect()  # 强制进行垃圾回收
        if 'cuda' in data_device:
            torch.cuda.empty_cache()  # 再次确保显存释放

    rstsdf = pd.DataFrame(rsts)
    mask_str = "_mask" if args.mask_prediction else ""
    rstsdf.to_csv(os.path.join(save_root, f'all_rst{mask_str}.csv'), index=False)
